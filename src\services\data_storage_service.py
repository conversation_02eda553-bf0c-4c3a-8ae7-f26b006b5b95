"""
Robust Data Storage Service.
Handles bulk insert optimization, data integrity checks, and duplicate prevention.
"""

import logging
import pandas as pd
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import IntegrityError

from src.database.models import (
    EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV,
    Symbol, DataStatistics, DataResumption, SymbolMapping, MarketType, OptionType
)
from src.core.config import settings

logger = logging.getLogger(__name__)


class DataStorageService:
    """
    Core data storage service with optimized bulk operations.
    Handles different market segments with appropriate table routing.
    """

    def __init__(self, db_session: Session):
        """Initialize the data storage service."""
        self.db = db_session
        self.batch_size = 1000  # Configurable batch size for bulk operations

        # Market type to model mapping
        self.market_models = {
            MarketType.EQUITY: EquityOHLCV,
            MarketType.INDEX: IndexOHLCV,
            MarketType.FUTURES: FuturesOHLCV,
            MarketType.OPTIONS: OptionsOHLCV
        }

    def _get_model_for_market_type(self, market_type: MarketType):
        """Get the appropriate model class for market type."""
        return self.market_models.get(market_type)

    def _prepare_ohlcv_data(self, data: List[Dict[str, Any]], market_type: MarketType) -> List[Dict[str, Any]]:
        """
        Prepare OHLCV data for insertion based on market type.

        Args:
            data: List of OHLCV data dictionaries
            market_type: Market type enum

        Returns:
            Prepared data list
        """
        prepared_data = []

        for record in data:
            # Ensure datetime is timezone-aware
            dt = record.get('datetime')
            if isinstance(dt, str):
                dt = pd.to_datetime(dt)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            base_record = {
                'datetime': dt,
                'symbol': record['symbol'],
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': int(record.get('volume', 0)),
                'created_at': datetime.now(timezone.utc)
            }

            # Add market-specific fields
            if market_type in [MarketType.FUTURES, MarketType.OPTIONS]:
                base_record['expiry_date'] = record.get('expiry_date')
                base_record['open_interest'] = int(record.get('open_interest', 0))

            if market_type == MarketType.OPTIONS:
                base_record['strike_price'] = float(record['strike_price'])
                base_record['option_type'] = OptionType(record['option_type'])

            prepared_data.append(base_record)

        return prepared_data

    def bulk_insert_ohlcv(self, data: List[Dict[str, Any]], market_type: MarketType,
                         symbol: str = None) -> Tuple[int, int]:
        """
        Bulk insert OHLCV data with duplicate handling.

        Args:
            data: List of OHLCV data dictionaries
            market_type: Market type enum
            symbol: Optional symbol filter for logging

        Returns:
            Tuple of (inserted_count, skipped_count)
        """
        if not data:
            return 0, 0

        model_class = self._get_model_for_market_type(market_type)
        if not model_class:
            raise ValueError(f"Unsupported market type: {market_type}")

        # Prepare data for insertion
        prepared_data = self._prepare_ohlcv_data(data, market_type)

        inserted_count = 0
        skipped_count = 0

        try:
            # Process in batches to avoid memory issues
            for i in range(0, len(prepared_data), self.batch_size):
                batch = prepared_data[i:i + self.batch_size]

                # Use PostgreSQL's ON CONFLICT DO NOTHING for duplicate handling
                stmt = insert(model_class).values(batch)

                # Define conflict resolution based on primary key
                if market_type == MarketType.OPTIONS:
                    stmt = stmt.on_conflict_do_nothing(
                        index_elements=['datetime', 'symbol', 'expiry_date', 'strike_price', 'option_type']
                    )
                elif market_type == MarketType.FUTURES:
                    stmt = stmt.on_conflict_do_nothing(
                        index_elements=['datetime', 'symbol', 'expiry_date']
                    )
                else:
                    stmt = stmt.on_conflict_do_nothing(
                        index_elements=['datetime', 'symbol']
                    )

                result = self.db.execute(stmt)
                batch_inserted = result.rowcount
                batch_skipped = len(batch) - batch_inserted

                inserted_count += batch_inserted
                skipped_count += batch_skipped

                if i % (self.batch_size * 10) == 0:  # Log every 10 batches
                    logger.debug(f"Processed {i + len(batch)}/{len(prepared_data)} records")

            self.db.commit()

            # Update statistics
            if symbol:
                self._update_data_statistics(symbol, market_type, len(data))

            logger.info(f"Bulk insert completed: {inserted_count} inserted, {skipped_count} skipped")
            return inserted_count, skipped_count

        except Exception as e:
            self.db.rollback()
            logger.error(f"Bulk insert failed: {e}")
            raise

    def _update_data_statistics(self, symbol: str, market_type: MarketType, record_count: int) -> None:
        """Update data statistics for a symbol."""
        try:
            # Get or create statistics record
            stats = self.db.query(DataStatistics).filter(
                and_(
                    DataStatistics.symbol == symbol,
                    DataStatistics.market_type == market_type
                )
            ).first()

            if not stats:
                stats = DataStatistics(
                    symbol=symbol,
                    market_type=market_type,
                    total_records=0
                )
                self.db.add(stats)

            # Update statistics
            model_class = self._get_model_for_market_type(market_type)
            if model_class:
                # Get actual record count and date range
                query = self.db.query(model_class).filter(model_class.symbol == symbol)

                total_records = query.count()
                first_record = query.order_by(model_class.datetime.asc()).first()
                last_record = query.order_by(model_class.datetime.desc()).first()

                stats.total_records = total_records
                stats.first_timestamp = first_record.datetime if first_record else None
                stats.last_timestamp = last_record.datetime if last_record else None
                stats.last_updated = datetime.now(timezone.utc)

            self.db.commit()

        except Exception as e:
            logger.error(f"Failed to update statistics for {symbol}: {e}")
            self.db.rollback()

    def get_data_range(self, symbol: str, market_type: MarketType,
                      start_date: datetime, end_date: datetime,
                      limit: int = None) -> List[Dict[str, Any]]:
        """
        Get OHLCV data for a symbol within date range.

        Args:
            symbol: Symbol name
            market_type: Market type enum
            start_date: Start date (timezone-aware)
            end_date: End date (timezone-aware)
            limit: Optional limit on number of records

        Returns:
            List of OHLCV data dictionaries
        """
        model_class = self._get_model_for_market_type(market_type)
        if not model_class:
            raise ValueError(f"Unsupported market type: {market_type}")

        try:
            query = self.db.query(model_class).filter(
                and_(
                    model_class.symbol == symbol,
                    model_class.datetime >= start_date,
                    model_class.datetime <= end_date
                )
            ).order_by(model_class.datetime.asc())

            if limit:
                query = query.limit(limit)

            records = query.all()

            # Convert to dictionaries
            result = []
            for record in records:
                data = {
                    'datetime': record.datetime,
                    'symbol': record.symbol,
                    'open': float(record.open),
                    'high': float(record.high),
                    'low': float(record.low),
                    'close': float(record.close),
                    'volume': record.volume
                }

                # Add market-specific fields
                if hasattr(record, 'expiry_date'):
                    data['expiry_date'] = record.expiry_date
                if hasattr(record, 'open_interest'):
                    data['open_interest'] = record.open_interest
                if hasattr(record, 'strike_price'):
                    data['strike_price'] = float(record.strike_price)
                if hasattr(record, 'option_type'):
                    data['option_type'] = record.option_type.value

                result.append(data)

            return result

        except Exception as e:
            logger.error(f"Failed to get data range for {symbol}: {e}")
            raise

    def get_latest_data(self, symbol: str, market_type: MarketType,
                       count: int = 100) -> List[Dict[str, Any]]:
        """
        Get latest OHLCV data for a symbol.

        Args:
            symbol: Symbol name
            market_type: Market type enum
            count: Number of latest records to return

        Returns:
            List of latest OHLCV data dictionaries
        """
        model_class = self._get_model_for_market_type(market_type)
        if not model_class:
            raise ValueError(f"Unsupported market type: {market_type}")

        try:
            records = self.db.query(model_class).filter(
                model_class.symbol == symbol
            ).order_by(model_class.datetime.desc()).limit(count).all()

            # Convert to dictionaries and reverse to get chronological order
            result = []
            for record in reversed(records):
                data = {
                    'datetime': record.datetime,
                    'symbol': record.symbol,
                    'open': float(record.open),
                    'high': float(record.high),
                    'low': float(record.low),
                    'close': float(record.close),
                    'volume': record.volume
                }

                # Add market-specific fields
                if hasattr(record, 'expiry_date'):
                    data['expiry_date'] = record.expiry_date
                if hasattr(record, 'open_interest'):
                    data['open_interest'] = record.open_interest
                if hasattr(record, 'strike_price'):
                    data['strike_price'] = float(record.strike_price)
                if hasattr(record, 'option_type'):
                    data['option_type'] = record.option_type.value

                result.append(data)

            return result

        except Exception as e:
            logger.error(f"Failed to get latest data for {symbol}: {e}")
            raise

    def get_data_summary(self, symbol: str, market_type: MarketType) -> Optional[Dict[str, Any]]:
        """
        Get data summary for a symbol.

        Args:
            symbol: Symbol name
            market_type: Market type enum

        Returns:
            Data summary dictionary or None if no data found
        """
        try:
            stats = self.db.query(DataStatistics).filter(
                and_(
                    DataStatistics.symbol == symbol,
                    DataStatistics.market_type == market_type
                )
            ).first()

            if stats:
                return {
                    'symbol': stats.symbol,
                    'market_type': stats.market_type.value,
                    'total_records': stats.total_records,
                    'first_timestamp': stats.first_timestamp,
                    'last_timestamp': stats.last_timestamp,
                    'last_updated': stats.last_updated
                }

            return None

        except Exception as e:
            logger.error(f"Failed to get data summary for {symbol}: {e}")
            return None

    def delete_data_range(self, symbol: str, market_type: MarketType,
                         start_date: datetime, end_date: datetime) -> int:
        """
        Delete OHLCV data for a symbol within date range.

        Args:
            symbol: Symbol name
            market_type: Market type enum
            start_date: Start date (timezone-aware)
            end_date: End date (timezone-aware)

        Returns:
            Number of deleted records
        """
        model_class = self._get_model_for_market_type(market_type)
        if not model_class:
            raise ValueError(f"Unsupported market type: {market_type}")

        try:
            deleted_count = self.db.query(model_class).filter(
                and_(
                    model_class.symbol == symbol,
                    model_class.datetime >= start_date,
                    model_class.datetime <= end_date
                )
            ).delete()

            self.db.commit()

            # Update statistics
            self._update_data_statistics(symbol, market_type, 0)

            logger.info(f"Deleted {deleted_count} records for {symbol}")
            return deleted_count

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete data for {symbol}: {e}")
            raise