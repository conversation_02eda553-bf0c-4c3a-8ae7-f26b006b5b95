"""
Historical Data Fetching Service.
Implements robust historical data fetching with automatic resumption, 
network failure handling, and progress tracking.
"""


import logging
import time
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import and_

project_root = Path(__file__).parent

from src.database.models import DataResumption, SymbolMapping, MarketType
from src.services.fyers_auth_service import FyersAuthService
from src.services.data_storage_service import DataStorageService
from src.core.config import settings

logger = logging.getLogger(__name__)


class HistoricalDataService:
    """
    Service for fetching historical data with resumption capability.
    Handles network failures, rate limiting, and progress tracking.
    """
    
    def __init__(self, db_session: Session):
        """Initialize the historical data service."""
        self.db = db_session
        self.fyers_auth = FyersAuthService()
        self.storage_service = DataStorageService(db_session)

        # Initialize Fyers authentication
        if not self.fyers_auth.initialize():
            logger.warning("Failed to initialize Fyers authentication")
        
        # Configuration from settings
        self.chunk_size_days = 90  # Fetch data in 90-day chunks
        self.max_retries = settings.rate_limit.max_retries
        self.retry_delay = settings.rate_limit.retry_backoff
        self.min_delay = settings.rate_limit.min_delay_seconds
        
        # Rate limiting
        self.last_request_time = 0
        self.request_count = 0
        
    def _apply_rate_limit(self) -> None:
        """Apply rate limiting between API requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_delay:
            sleep_time = self.min_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def _convert_to_fyers_symbol(self, symbol: str, market_type: MarketType) -> str:
        """
        Convert NSE symbol to Fyers format.
        
        Args:
            symbol: NSE symbol
            market_type: Market type enum
            
        Returns:
            Fyers formatted symbol
        """
        # Check symbol mapping table first
        mapping = self.db.query(SymbolMapping).filter(
            and_(
                SymbolMapping.nse_symbol == symbol,
                SymbolMapping.market_type == market_type,
                SymbolMapping.is_active == True
            )
        ).first()
        
        if mapping:
            return mapping.fyers_symbol
        
        # Default conversion logic
        if market_type == MarketType.EQUITY:
            return f"NSE:{symbol}-EQ"
        elif market_type == MarketType.INDEX:
            return f"NSE:{symbol}-INDEX"
        elif market_type == MarketType.FUTURES:
            # For futures, we need expiry information
            # This is a simplified conversion - in practice, you'd need more logic
            return f"NSE:{symbol}-FUT"
        elif market_type == MarketType.OPTIONS:
            # For options, we need strike and type information
            # This is a simplified conversion - in practice, you'd need more logic
            return f"NSE:{symbol}-OPT"
        else:
            return f"NSE:{symbol}"
    
    def _fetch_fyers_data(self, symbol: str, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        Fetch data from Fyers API.
        
        Args:
            symbol: Fyers symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            List of OHLCV data dictionaries
        """
        self._apply_rate_limit()

        if not self.fyers_auth.is_authenticated():
            raise Exception("Fyers service not authenticated")

        try:
            # Calculate days to fetch
            days_to_fetch = (end_date - start_date).days

            # Use the new FyersAuthService method
            data = self.fyers_auth.fetch_historical_data_chunked(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                interval=1  # 1-minute data
            )

            return data

        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            raise

    
    def _create_resumption_record(self, symbol: str, market_type: MarketType, 
                                 start_date: datetime, end_date: datetime) -> DataResumption:
        """Create a data resumption record."""
        resumption = DataResumption(
            symbol=symbol,
            market_type=market_type,
            start_date=start_date,
            end_date=end_date,
            status="PENDING"
        )
        self.db.add(resumption)
        self.db.commit()
        return resumption
    
    def _update_resumption_record(self, resumption: DataResumption, 
                                 last_fetched_date: datetime = None,
                                 status: str = None, error_message: str = None) -> None:
        """Update a data resumption record."""
        if last_fetched_date:
            resumption.last_fetched_date = last_fetched_date
        if status:
            resumption.status = status
        if error_message:
            resumption.error_message = error_message
        
        resumption.updated_at = datetime.now(timezone.utc)
        self.db.commit()
    
    def fetch_historical_data(self, symbol: str, market_type: MarketType,
                            start_date: datetime, end_date: datetime,
                            resume: bool = True) -> Dict[str, Any]:
        """
        Fetch historical data for a symbol with resumption capability.
        
        Args:
            symbol: Symbol name
            market_type: Market type enum
            start_date: Start date (timezone-aware)
            end_date: End date (timezone-aware)
            resume: Whether to resume from previous attempt
            
        Returns:
            Dictionary with fetch results
        """
        logger.info(f"Starting historical data fetch for {symbol} ({market_type.value})")
        
        # Check for existing resumption record
        resumption = None
        if resume:
            resumption = self.db.query(DataResumption).filter(
                and_(
                    DataResumption.symbol == symbol,
                    DataResumption.market_type == market_type,
                    DataResumption.status.in_(["PENDING", "IN_PROGRESS", "FAILED"])
                )
            ).first()
        
        # Create new resumption record if none exists
        if not resumption:
            resumption = self._create_resumption_record(symbol, market_type, start_date, end_date)
        
        # Determine fetch range
        fetch_start = resumption.last_fetched_date or start_date
        fetch_end = end_date
        
        if fetch_start >= fetch_end:
            logger.info(f"Data already up to date for {symbol}")
            self._update_resumption_record(resumption, status="COMPLETED")
            return {"status": "completed", "message": "Data already up to date"}
        
        # Update status to in progress
        self._update_resumption_record(resumption, status="IN_PROGRESS")
        
        total_records = 0
        total_chunks = 0
        current_date = fetch_start
        
        try:
            # Convert to Fyers symbol format
            fyers_symbol = self._convert_to_fyers_symbol(symbol, market_type)
            
            while current_date < fetch_end:
                chunk_end = min(current_date + timedelta(days=self.chunk_size_days), fetch_end)
                
                logger.info(f"Fetching chunk: {current_date.date()} to {chunk_end.date()}")
                
                retry_count = 0
                chunk_data = None
                
                # Retry logic for network failures
                while retry_count < self.max_retries:
                    try:
                        chunk_data = self._fetch_fyers_data(fyers_symbol, current_date, chunk_end)
                        break
                    except Exception as e:
                        retry_count += 1
                        if retry_count < self.max_retries:
                            wait_time = self.retry_delay * (2 ** (retry_count - 1))
                            logger.warning(f"Fetch failed, retrying in {wait_time}s (attempt {retry_count}/{self.max_retries}): {e}")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"Max retries exceeded for chunk {current_date.date()}")
                            raise
                
                # Store the chunk data
                if chunk_data:
                    inserted, skipped = self.storage_service.bulk_insert_ohlcv(
                        chunk_data, market_type, symbol
                    )
                    total_records += inserted
                    logger.info(f"Chunk stored: {inserted} inserted, {skipped} skipped")
                
                # Update resumption record
                self._update_resumption_record(resumption, last_fetched_date=chunk_end)
                
                current_date = chunk_end
                total_chunks += 1
                
                # Small delay between chunks
                time.sleep(0.5)
            
            # Mark as completed
            self._update_resumption_record(resumption, status="COMPLETED")
            
            result = {
                "status": "completed",
                "symbol": symbol,
                "market_type": market_type.value,
                "total_records": total_records,
                "total_chunks": total_chunks,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
            
            logger.info(f"Historical data fetch completed for {symbol}: {total_records} records")
            return result
            
        except Exception as e:
            # Mark as failed and update error message
            self._update_resumption_record(
                resumption, 
                status="FAILED", 
                error_message=str(e)
            )
            
            logger.error(f"Historical data fetch failed for {symbol}: {e}")
            raise
    
    def get_resumption_status(self, symbol: str, market_type: MarketType) -> Optional[Dict[str, Any]]:
        """
        Get resumption status for a symbol.
        
        Args:
            symbol: Symbol name
            market_type: Market type enum
            
        Returns:
            Resumption status dictionary or None
        """
        resumption = self.db.query(DataResumption).filter(
            and_(
                DataResumption.symbol == symbol,
                DataResumption.market_type == market_type
            )
        ).order_by(DataResumption.updated_at.desc()).first()
        
        if resumption:
            return {
                "symbol": resumption.symbol,
                "market_type": resumption.market_type.value,
                "status": resumption.status,
                "start_date": resumption.start_date.isoformat(),
                "end_date": resumption.end_date.isoformat(),
                "last_fetched_date": resumption.last_fetched_date.isoformat() if resumption.last_fetched_date else None,
                "error_message": resumption.error_message,
                "retry_count": resumption.retry_count,
                "updated_at": resumption.updated_at.isoformat()
            }
        
        return None
