"""
Enhanced Symbol Service with Universal Symbol Parser Integration.
Provides comprehensive symbol management with multi-market support.
"""

import logging
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime

from src.core.universal_symbol_parser import UniversalSymbolParser
from src.auth.config_loader import get_config
from src.database.models import Symbol, SymbolMapping, MarketType
from src.core.config import settings

logger = logging.getLogger(__name__)


class EnhancedSymbolService:
    """Enhanced symbol service with universal parser integration."""

    def __init__(self, db_session: Session, target_symbols: List[str] = None):
        """
        Initialize the enhanced symbol service.
        
        Args:
            db_session: Database session
            target_symbols: List of target symbols to filter by (optional)
        """
        self.db = db_session
        self.config = get_config()
        
        # Initialize universal symbol parser
        self.parser = UniversalSymbolParser(
            config=self.config,
            target_symbols=target_symbols or []
        )
        
        logger.info("Enhanced symbol service initialized")

    def fetch_and_populate_all_symbols(self, force_refresh: bool = False) -> Dict[str, int]:
        """
        Fetch symbols from Fyers API and populate database for all market types.
        
        Args:
            force_refresh: Whether to force refresh existing symbols
            
        Returns:
            Dictionary with counts of processed symbols by market type
        """
        logger.info("🚀 Starting comprehensive symbol fetch and population...")
        
        try:
            # Use the universal parser's download and populate method
            results = self.parser.download_and_populate_symbols(self.db, force_refresh)
            
            logger.info(f"✅ Symbol fetch and population completed successfully")
            logger.info(f"📊 Results: {results}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in symbol fetch and population: {e}")
            self.db.rollback()
            raise

    def get_symbols_by_market_type(self, market_type: MarketType, 
                                 underlying_filter: List[str] = None,
                                 limit: int = None) -> List[Dict]:
        """
        Get symbols from database by market type.
        
        Args:
            market_type: Market type to filter by
            underlying_filter: List of underlying symbols to filter by
            limit: Maximum number of symbols to return
            
        Returns:
            List of symbol dictionaries
        """
        try:
            query = self.db.query(Symbol).filter(
                Symbol.market_type == market_type,
                Symbol.is_active == True
            )
            
            if underlying_filter:
                # For underlying filter, we need to check the symbol name patterns
                # This is a simplified approach - in production you might want more sophisticated filtering
                underlying_patterns = [f"%{underlying}%" for underlying in underlying_filter]
                from sqlalchemy import or_
                query = query.filter(
                    or_(*[Symbol.symbol.ilike(pattern) for pattern in underlying_patterns])
                )
            
            if limit:
                query = query.limit(limit)
                
            symbols = query.all()
            
            result = []
            for symbol in symbols:
                result.append({
                    'symbol': symbol.symbol,
                    'name': symbol.name,
                    'market_type': symbol.market_type.value,
                    'exchange': symbol.exchange,
                    'is_active': symbol.is_active
                })
            
            logger.info(f"Retrieved {len(result)} {market_type.value} symbols from database")
            return result
            
        except Exception as e:
            logger.error(f"Error retrieving symbols by market type {market_type}: {e}")
            return []

    def get_symbol_mapping(self, nse_symbol: str, market_type: MarketType) -> Optional[Dict]:
        """
        Get symbol mapping for a given NSE symbol and market type.
        
        Args:
            nse_symbol: NSE symbol
            market_type: Market type
            
        Returns:
            Symbol mapping dictionary or None
        """
        try:
            mapping = self.db.query(SymbolMapping).filter(
                SymbolMapping.nse_symbol == nse_symbol,
                SymbolMapping.market_type == market_type,
                SymbolMapping.is_active == True
            ).first()
            
            if mapping:
                return {
                    'nse_symbol': mapping.nse_symbol,
                    'fyers_symbol': mapping.fyers_symbol,
                    'market_type': mapping.market_type.value,
                    'exchange': mapping.exchange,
                    'expiry_date': mapping.expiry_date.isoformat() if mapping.expiry_date else None,
                    'strike_price': float(mapping.strike_price) if mapping.strike_price else None,
                    'option_type': mapping.option_type.value if mapping.option_type else None
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving symbol mapping for {nse_symbol}: {e}")
            return None

    def get_symbols_for_data_loading(self, market_type: MarketType, 
                                   target_symbols: List[str] = None,
                                   limit: int = None) -> List[str]:
        """
        Get symbols ready for data loading operations.
        
        Args:
            market_type: Market type to get symbols for
            target_symbols: Specific symbols to target
            limit: Maximum number of symbols to return
            
        Returns:
            List of NSE symbols ready for data loading
        """
        try:
            # Use the universal parser to get symbols for the market type
            symbols = self.parser.get_symbols_for_market_type(
                market_type.value,
                underlying_symbols=target_symbols,
                limit_symbols=limit
            )
            
            logger.info(f"Retrieved {len(symbols)} {market_type.value} symbols for data loading")
            return symbols
            
        except Exception as e:
            logger.error(f"Error getting symbols for data loading: {e}")
            return []

    def validate_symbol_data_integrity(self) -> Dict[str, int]:
        """
        Validate the integrity of symbol data in the database.
        
        Returns:
            Dictionary with validation results
        """
        try:
            results = {
                'total_symbols': 0,
                'total_mappings': 0,
                'orphaned_mappings': 0,
                'missing_mappings': 0
            }
            
            # Count total symbols
            results['total_symbols'] = self.db.query(Symbol).filter(Symbol.is_active == True).count()
            
            # Count total mappings
            results['total_mappings'] = self.db.query(SymbolMapping).filter(SymbolMapping.is_active == True).count()
            
            # Find orphaned mappings (mappings without corresponding symbols)
            orphaned_mappings = self.db.query(SymbolMapping).filter(
                SymbolMapping.is_active == True,
                ~SymbolMapping.nse_symbol.in_(
                    self.db.query(Symbol.symbol).filter(Symbol.is_active == True)
                )
            ).count()
            results['orphaned_mappings'] = orphaned_mappings
            
            # Find symbols without mappings
            missing_mappings = self.db.query(Symbol).filter(
                Symbol.is_active == True,
                ~Symbol.symbol.in_(
                    self.db.query(SymbolMapping.nse_symbol).filter(SymbolMapping.is_active == True)
                )
            ).count()
            results['missing_mappings'] = missing_mappings
            
            logger.info(f"Symbol data integrity validation completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error validating symbol data integrity: {e}")
            return {}

    def get_market_type_summary(self) -> Dict[str, Dict]:
        """
        Get summary of symbols by market type.
        
        Returns:
            Dictionary with market type summaries
        """
        try:
            summary = {}
            
            for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                count = self.db.query(Symbol).filter(
                    Symbol.market_type == market_type,
                    Symbol.is_active == True
                ).count()
                
                mapping_count = self.db.query(SymbolMapping).filter(
                    SymbolMapping.market_type == market_type,
                    SymbolMapping.is_active == True
                ).count()
                
                summary[market_type.value] = {
                    'symbol_count': count,
                    'mapping_count': mapping_count
                }
            
            logger.info(f"Market type summary: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"Error getting market type summary: {e}")
            return {}
