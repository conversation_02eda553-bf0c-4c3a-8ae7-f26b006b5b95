"""
Comprehensive Data Management Service.
Handles duplicate removal, data integrity fixes, and ensures 100% CSV-DB parity.
"""

import logging
import async<PERSON>
from typing import Dict, List, Optional, Any
from datetime import datetime

from .duplicate_removal_service import DuplicateRemovalService
from ..core.data_integrity_validator import DataIntegrityValidator
from ..core.nse_symbol_processor import NSESymbolProcessor
from ..core.config import settings

logger = logging.getLogger(__name__)


class DataManagementService:
    """Comprehensive service for managing data integrity and ensuring CSV-DB parity."""
    
    def __init__(self):
        """Initialize the data management service."""
        self.duplicate_service = DuplicateRemovalService()
        self.integrity_validator = DataIntegrityValidator()
        self.symbol_processor = NSESymbolProcessor()
        
    def fix_all_data_issues(self, create_backup: bool = True) -> Dict[str, Any]:
        """
        Comprehensive fix for all data issues:
        1. Remove duplicates from raw tables
        2. Ensure 100% CSV-DB parity
        3. Validate data integrity
        4. Generate detailed report
        """
        logger.info("🚀 Starting comprehensive data management process...")
        
        results = {
            'backup_created': False,
            'duplicates_removed': False,
            'symbol_processing_completed': False,
            'data_integrity_validated': False,
            'issues_found': [],
            'recommendations': [],
            'summary': {}
        }
        
        try:
            # Step 1: Create backup if requested
            if create_backup:
                logger.info("📦 Creating backup tables...")
                backup_info = self.duplicate_service.create_backup_before_removal()
                results['backup_created'] = True
                results['backup_info'] = backup_info
                logger.info("✅ Backup tables created successfully")
            
            # Step 2: Analyze current state
            logger.info("🔍 Analyzing current data state...")
            initial_stats = self.duplicate_service.get_duplicate_statistics()
            results['initial_statistics'] = initial_stats
            
            # Step 3: Remove duplicates
            logger.info("🧹 Removing duplicate entries...")
            duplicate_results = self.duplicate_service.remove_all_duplicates()
            results['duplicate_removal_results'] = duplicate_results
            results['duplicates_removed'] = True
            
            # Verify duplicate removal
            integrity_check = self.duplicate_service.verify_data_integrity_after_removal()
            results['duplicate_removal_verified'] = all(integrity_check.values())
            
            # Step 4: Re-process NSE symbols to ensure 100% parity
            logger.info("📥 Re-processing NSE symbols to ensure 100% CSV-DB parity...")
            symbol_results = self.symbol_processor.process_nse_files()
            results['symbol_processing_results'] = symbol_results
            results['symbol_processing_completed'] = all(symbol_results.values())
            
            # Step 5: Comprehensive data integrity validation
            logger.info("🔍 Running comprehensive data integrity validation...")
            validation_results = self.integrity_validator.validate_all()
            results['validation_results'] = validation_results
            results['data_integrity_validated'] = True
            
            # Step 6: Generate summary and recommendations
            self._generate_comprehensive_summary(results)
            
            logger.info("✅ Comprehensive data management process completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error during comprehensive data management: {e}")
            results['error'] = str(e)
            return results
    
    def _generate_comprehensive_summary(self, results: Dict[str, Any]) -> None:
        """Generate comprehensive summary and recommendations."""
        summary = {
            'overall_status': 'SUCCESS',
            'total_issues_fixed': 0,
            'remaining_issues': 0,
            'data_quality_score': 0.0,
            'recommendations': []
        }
        
        issues_found = []
        recommendations = []
        
        # Analyze duplicate removal results
        if results.get('duplicates_removed'):
            duplicate_stats = results.get('duplicate_removal_results', {})
            total_duplicates_removed = 0
            for table, stats in duplicate_stats.items():
                removed = stats.get('duplicates_removed', 0)
                total_duplicates_removed += removed
                if removed > 0:
                    logger.info(f"✅ Removed {removed:,} duplicates from {table}")
            
            summary['total_issues_fixed'] += total_duplicates_removed
            
            if not results.get('duplicate_removal_verified', False):
                issues_found.append("Some duplicates may still exist after removal")
                recommendations.append("Re-run duplicate removal process")
        
        # Analyze symbol processing results
        if results.get('symbol_processing_completed'):
            symbol_results = results.get('symbol_processing_results', {})
            if not all(symbol_results.values()):
                failed_steps = [step for step, success in symbol_results.items() if not success]
                issues_found.append(f"Symbol processing failed for: {', '.join(failed_steps)}")
                recommendations.append("Re-run NSE symbol processing")
        
        # Analyze validation results
        validation_results = results.get('validation_results', {})
        if validation_results:
            # Check CSV-DB parity
            parity_results = validation_results.get('csv_database_parity', {})
            for source, parity_info in parity_results.items():
                if isinstance(parity_info, dict):
                    parity_percentage = parity_info.get('parity_percentage', 0)
                    if parity_percentage < 99.0:
                        missing_count = parity_info.get('missing_count', 0)
                        issues_found.append(f"{source}: {missing_count:,} symbols missing ({parity_percentage:.1f}% parity)")
                        recommendations.append(f"Re-process {source} symbols to achieve 100% parity")
            
            # Check duplicate analysis
            duplicate_analysis = validation_results.get('duplicate_analysis', {})
            for table, analysis in duplicate_analysis.items():
                if isinstance(analysis, dict) and analysis.get('status') == 'NEEDS_CLEANUP':
                    duplicate_count = analysis.get('duplicate_rows', 0)
                    if duplicate_count > 0:
                        issues_found.append(f"{table}: {duplicate_count:,} duplicate rows still exist")
                        recommendations.append(f"Re-run duplicate removal for {table}")
            
            # Check missing symbols
            missing_analysis = validation_results.get('missing_symbols_analysis', {})
            for market_type, analysis in missing_analysis.items():
                if isinstance(analysis, dict):
                    missing_count = analysis.get('missing_from_mapping', 0)
                    if missing_count > 0:
                        issues_found.append(f"{market_type}: {missing_count:,} symbols missing from mapping")
                        recommendations.append(f"Re-process {market_type} symbols")
        
        # Calculate data quality score
        total_issues = len(issues_found)
        if total_issues == 0:
            summary['data_quality_score'] = 100.0
            summary['overall_status'] = 'EXCELLENT'
        elif total_issues <= 2:
            summary['data_quality_score'] = 85.0
            summary['overall_status'] = 'GOOD'
        elif total_issues <= 5:
            summary['data_quality_score'] = 70.0
            summary['overall_status'] = 'FAIR'
        else:
            summary['data_quality_score'] = 50.0
            summary['overall_status'] = 'NEEDS_ATTENTION'
        
        summary['remaining_issues'] = total_issues
        summary['issues_found'] = issues_found
        summary['recommendations'] = recommendations
        
        results['summary'] = summary
        results['issues_found'] = issues_found
        results['recommendations'] = recommendations
        
        # Log summary
        logger.info(f"\n📊 Data Management Summary:")
        logger.info(f"   Overall Status: {summary['overall_status']}")
        logger.info(f"   Data Quality Score: {summary['data_quality_score']:.1f}%")
        logger.info(f"   Issues Fixed: {summary['total_issues_fixed']:,}")
        logger.info(f"   Remaining Issues: {summary['remaining_issues']}")
        
        if issues_found:
            logger.warning(f"\n⚠️  Issues Found:")
            for issue in issues_found:
                logger.warning(f"   - {issue}")
        
        if recommendations:
            logger.info(f"\n💡 Recommendations:")
            for rec in recommendations:
                logger.info(f"   - {rec}")
    
    def get_data_health_report(self) -> Dict[str, Any]:
        """Get comprehensive data health report."""
        logger.info("📋 Generating data health report...")
        
        try:
            # Get duplicate statistics
            duplicate_stats = self.duplicate_service.get_duplicate_statistics()
            
            # Run validation
            validation_results = self.integrity_validator.validate_all()
            
            # Combine into health report
            health_report = {
                'timestamp': datetime.now().isoformat(),
                'duplicate_statistics': duplicate_stats,
                'validation_results': validation_results,
                'overall_health': self._calculate_overall_health(duplicate_stats, validation_results)
            }
            
            return health_report
            
        except Exception as e:
            logger.error(f"Error generating data health report: {e}")
            return {'error': str(e)}
    
    def _calculate_overall_health(self, duplicate_stats: Dict, validation_results: Dict) -> Dict[str, Any]:
        """Calculate overall data health score."""
        health_score = 100.0
        issues = []
        
        # Check duplicates
        for table, stats in duplicate_stats.items():
            duplicate_percentage = stats.get('duplicate_percentage', 0)
            if duplicate_percentage > 0:
                health_score -= duplicate_percentage * 0.5  # Reduce score based on duplicate percentage
                issues.append(f"{table}: {duplicate_percentage:.1f}% duplicates")
        
        # Check validation results
        summary = validation_results.get('summary', {})
        total_issues = summary.get('total_issues', 0)
        if total_issues > 0:
            health_score -= total_issues * 5  # Reduce 5 points per issue
            issues.extend(summary.get('issues', []))
        
        # Ensure score doesn't go below 0
        health_score = max(0.0, health_score)
        
        # Determine health status
        if health_score >= 95:
            status = 'EXCELLENT'
        elif health_score >= 85:
            status = 'GOOD'
        elif health_score >= 70:
            status = 'FAIR'
        else:
            status = 'POOR'
        
        return {
            'score': round(health_score, 1),
            'status': status,
            'issues_count': len(issues),
            'issues': issues
        }
    
    async def monitor_data_quality(self, interval_minutes: int = 60) -> None:
        """Monitor data quality continuously."""
        logger.info(f"🔄 Starting data quality monitoring (interval: {interval_minutes} minutes)")
        
        while True:
            try:
                health_report = self.get_data_health_report()
                overall_health = health_report.get('overall_health', {})
                
                logger.info(f"📊 Data Health: {overall_health.get('status')} ({overall_health.get('score')}%)")
                
                if overall_health.get('score', 0) < 80:
                    logger.warning("⚠️ Data quality below threshold - consider running maintenance")
                
                # Wait for next check
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in data quality monitoring: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retry
