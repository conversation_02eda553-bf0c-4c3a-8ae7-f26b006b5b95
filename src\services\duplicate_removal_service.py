"""
Duplicate Data Removal Service for NSE Raw Tables.
Identifies and removes duplicate entries while preserving data integrity.
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from ..core.config import settings

logger = logging.getLogger(__name__)


class DuplicateRemovalService:
    """Service to identify and remove duplicate entries from NSE raw tables."""
    
    def __init__(self):
        """Initialize the duplicate removal service."""
        self.db_url = settings.database.url
        self.removal_stats = {
            'nse_cm_raw': {'duplicates_found': 0, 'duplicates_removed': 0},
            'nse_fo_raw': {'duplicates_found': 0, 'duplicates_removed': 0}
        }
    
    def remove_all_duplicates(self) -> Dict[str, Dict[str, int]]:
        """Remove duplicates from all NSE raw tables."""
        logger.info("Starting duplicate removal process for all NSE raw tables...")
        
        try:
            # Remove duplicates from NSE_CM table
            self._remove_duplicates_from_table('nse_cm_raw')
            
            # Remove duplicates from NSE_FO table
            self._remove_duplicates_from_table('nse_fo_raw')
            
            logger.info("Duplicate removal process completed successfully")
            return self.removal_stats
            
        except Exception as e:
            logger.error(f"Error during duplicate removal: {e}")
            raise
    
    def _remove_duplicates_from_table(self, table_name: str) -> None:
        """Remove duplicates from a specific table."""
        logger.info(f"Processing duplicates in {table_name}...")
        
        try:
            conn = psycopg2.connect(self.db_url)
            conn.autocommit = False
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # First, identify duplicates
                duplicates = self._identify_duplicates(cursor, table_name)
                self.removal_stats[table_name]['duplicates_found'] = len(duplicates)
                
                if not duplicates:
                    logger.info(f"No duplicates found in {table_name}")
                    return
                
                logger.info(f"Found {len(duplicates)} duplicate groups in {table_name}")
                
                # Remove duplicates while keeping the first occurrence (lowest ID)
                removed_count = self._remove_duplicate_rows(cursor, table_name, duplicates)
                self.removal_stats[table_name]['duplicates_removed'] = removed_count
                
                # Commit the transaction
                conn.commit()
                logger.info(f"Successfully removed {removed_count} duplicate rows from {table_name}")
                
        except Exception as e:
            logger.error(f"Error removing duplicates from {table_name}: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    def _identify_duplicates(self, cursor, table_name: str) -> List[Dict]:
        """Identify duplicate rows based on key fields."""
        logger.debug(f"Identifying duplicates in {table_name}...")
        
        # Define key fields for duplicate detection
        if table_name == 'nse_cm_raw':
            key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id']
        elif table_name == 'nse_fo_raw':
            key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id', 'expiry_timestamp']
        else:
            raise ValueError(f"Unknown table: {table_name}")
        
        # Find groups of duplicate rows
        key_fields_str = ', '.join(key_fields)
        query = f"""
            SELECT {key_fields_str}, COUNT(*) as duplicate_count, 
                   ARRAY_AGG(id ORDER BY id) as ids
            FROM {table_name}
            WHERE {' AND '.join([f'{field} IS NOT NULL' for field in key_fields])}
            GROUP BY {key_fields_str}
            HAVING COUNT(*) > 1
            ORDER BY duplicate_count DESC
        """
        
        cursor.execute(query)
        duplicates = cursor.fetchall()
        
        logger.debug(f"Found {len(duplicates)} duplicate groups in {table_name}")
        return duplicates
    
    def _remove_duplicate_rows(self, cursor, table_name: str, duplicates: List[Dict]) -> int:
        """Remove duplicate rows, keeping only the first occurrence."""
        total_removed = 0
        
        for duplicate_group in duplicates:
            ids = duplicate_group['ids']
            # Keep the first ID (lowest), remove the rest
            ids_to_remove = ids[1:]  # Skip the first ID
            
            if ids_to_remove:
                # Convert list to tuple for SQL IN clause
                ids_tuple = tuple(ids_to_remove)
                
                if len(ids_tuple) == 1:
                    query = f"DELETE FROM {table_name} WHERE id = %s"
                    cursor.execute(query, (ids_tuple[0],))
                else:
                    query = f"DELETE FROM {table_name} WHERE id IN %s"
                    cursor.execute(query, (ids_tuple,))
                
                removed_count = cursor.rowcount
                total_removed += removed_count
                
                logger.debug(f"Removed {removed_count} duplicates for group with IDs: {ids}")
        
        return total_removed
    
    def get_duplicate_statistics(self) -> Dict[str, Dict[str, int]]:
        """Get statistics about duplicates in NSE raw tables."""
        logger.info("Gathering duplicate statistics...")
        
        stats = {}
        
        try:
            conn = psycopg2.connect(self.db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for table_name in ['nse_cm_raw', 'nse_fo_raw']:
                    # Get total rows
                    cursor.execute(f"SELECT COUNT(*) as total_rows FROM {table_name}")
                    total_rows = cursor.fetchone()['total_rows']
                    
                    # Get unique rows based on key fields
                    if table_name == 'nse_cm_raw':
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id']
                    else:  # nse_fo_raw
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id', 'expiry_timestamp']
                    
                    key_fields_str = ', '.join(key_fields)
                    cursor.execute(f"""
                        SELECT COUNT(DISTINCT ({key_fields_str})) as unique_rows 
                        FROM {table_name}
                        WHERE {' AND '.join([f'{field} IS NOT NULL' for field in key_fields])}
                    """)
                    unique_rows = cursor.fetchone()['unique_rows']
                    
                    duplicate_rows = total_rows - unique_rows
                    
                    stats[table_name] = {
                        'total_rows': total_rows,
                        'unique_rows': unique_rows,
                        'duplicate_rows': duplicate_rows,
                        'duplicate_percentage': round((duplicate_rows / total_rows * 100), 2) if total_rows > 0 else 0
                    }
                    
                    logger.info(f"{table_name}: {total_rows:,} total, {unique_rows:,} unique, {duplicate_rows:,} duplicates ({stats[table_name]['duplicate_percentage']}%)")
            
            conn.close()
            return stats
            
        except Exception as e:
            logger.error(f"Error gathering duplicate statistics: {e}")
            raise
    
    def verify_data_integrity_after_removal(self) -> Dict[str, bool]:
        """Verify data integrity after duplicate removal."""
        logger.info("Verifying data integrity after duplicate removal...")
        
        integrity_results = {}
        
        try:
            conn = psycopg2.connect(self.db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for table_name in ['nse_cm_raw', 'nse_fo_raw']:
                    # Check for any remaining duplicates
                    if table_name == 'nse_cm_raw':
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id']
                    else:  # nse_fo_raw
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id', 'expiry_timestamp']
                    
                    key_fields_str = ', '.join(key_fields)
                    cursor.execute(f"""
                        SELECT COUNT(*) as remaining_duplicates
                        FROM (
                            SELECT {key_fields_str}, COUNT(*) as cnt
                            FROM {table_name}
                            WHERE {' AND '.join([f'{field} IS NOT NULL' for field in key_fields])}
                            GROUP BY {key_fields_str}
                            HAVING COUNT(*) > 1
                        ) duplicates
                    """)
                    
                    remaining_duplicates = cursor.fetchone()['remaining_duplicates']
                    integrity_results[table_name] = remaining_duplicates == 0
                    
                    if remaining_duplicates == 0:
                        logger.info(f"✅ {table_name}: No remaining duplicates found")
                    else:
                        logger.warning(f"⚠️ {table_name}: {remaining_duplicates} duplicate groups still exist")
            
            conn.close()
            return integrity_results
            
        except Exception as e:
            logger.error(f"Error verifying data integrity: {e}")
            raise
    
    def create_backup_before_removal(self) -> Dict[str, str]:
        """Create backup tables before removing duplicates."""
        logger.info("Creating backup tables before duplicate removal...")
        
        backup_info = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            conn = psycopg2.connect(self.db_url)
            
            with conn.cursor() as cursor:
                for table_name in ['nse_cm_raw', 'nse_fo_raw']:
                    backup_table_name = f"{table_name}_backup_{timestamp}"
                    
                    # Create backup table
                    cursor.execute(f"""
                        CREATE TABLE {backup_table_name} AS 
                        SELECT * FROM {table_name}
                    """)
                    
                    # Get row count for verification
                    cursor.execute(f"SELECT COUNT(*) FROM {backup_table_name}")
                    backup_count = cursor.fetchone()[0]
                    
                    backup_info[table_name] = {
                        'backup_table': backup_table_name,
                        'backup_count': backup_count
                    }
                    
                    logger.info(f"Created backup table {backup_table_name} with {backup_count:,} rows")
            
            conn.commit()
            conn.close()
            
            return backup_info
            
        except Exception as e:
            logger.error(f"Error creating backup tables: {e}")
            raise
