#!/usr/bin/env python3
"""
Data Validation Script for Symbol Loading and Data Integrity.
Validates that total data loaded in database matches data fetched from Fyers API.
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

import logging
from datetime import datetime, timedelta
from database.connection import get_db, check_database_connection
from services.enhanced_symbol_service import EnhancedSymbolService
from database.models import MarketType
from core.logging import setup_enhanced_logging
from sqlalchemy import text

# Setup logging
setup_enhanced_logging()
logger = logging.getLogger(__name__)


def validate_symbol_data_integrity():
    """Validate symbol data integrity between CSV downloads and database."""
    logger.info("🔍 Validating symbol data integrity...")
    
    try:
        if not check_database_connection():
            logger.error("❌ Database connection failed")
            return False
        
        db = next(get_db())
        
        try:
            symbol_service = EnhancedSymbolService(db)
            
            # Get integrity validation results
            integrity_results = symbol_service.validate_symbol_data_integrity()
            
            logger.info("📊 Symbol Data Integrity Results:")
            logger.info(f"  Total symbols: {integrity_results.get('total_symbols', 0):,}")
            logger.info(f"  Total mappings: {integrity_results.get('total_mappings', 0):,}")
            logger.info(f"  Orphaned mappings: {integrity_results.get('orphaned_mappings', 0):,}")
            logger.info(f"  Missing mappings: {integrity_results.get('missing_mappings', 0):,}")
            
            # Get market type summary
            summary = symbol_service.get_market_type_summary()
            logger.info("\n📈 Market Type Summary:")
            for market_type, data in summary.items():
                logger.info(f"  {market_type}: {data['symbol_count']:,} symbols, {data['mapping_count']:,} mappings")
            
            # Check for data integrity issues
            issues = []
            if integrity_results.get('orphaned_mappings', 0) > 0:
                issues.append(f"Found {integrity_results['orphaned_mappings']} orphaned mappings")
            if integrity_results.get('missing_mappings', 0) > 0:
                issues.append(f"Found {integrity_results['missing_mappings']} symbols without mappings")
            
            if issues:
                logger.warning("⚠️  Data integrity issues found:")
                for issue in issues:
                    logger.warning(f"    - {issue}")
                return False
            else:
                logger.info("✅ Symbol data integrity validation passed")
                return True
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in symbol data integrity validation: {e}")
        return False


def validate_ohlcv_data_counts():
    """Validate OHLCV data counts across different market types."""
    logger.info("\n🔍 Validating OHLCV data counts...")
    
    try:
        db = next(get_db())
        
        try:
            # Check data counts for each market type table
            tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
            
            logger.info("📊 OHLCV Data Counts by Table:")
            total_records = 0
            
            for table in tables:
                try:
                    result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    total_records += count
                    logger.info(f"  {table}: {count:,} records")
                    
                    # Get date range for this table
                    if count > 0:
                        result = db.execute(text(f"SELECT MIN(datetime), MAX(datetime) FROM {table}"))
                        min_date, max_date = result.fetchone()
                        logger.info(f"    Date range: {min_date} to {max_date}")
                        
                        # Get unique symbols count
                        result = db.execute(text(f"SELECT COUNT(DISTINCT symbol) FROM {table}"))
                        unique_symbols = result.scalar()
                        logger.info(f"    Unique symbols: {unique_symbols}")
                        
                except Exception as e:
                    logger.error(f"    Error checking {table}: {e}")
            
            logger.info(f"\n📈 Total OHLCV records across all tables: {total_records:,}")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in OHLCV data validation: {e}")
        return False


def validate_symbol_mapping_usage():
    """Validate that symbol mappings are being used correctly."""
    logger.info("\n🔍 Validating symbol mapping usage...")
    
    try:
        db = next(get_db())
        
        try:
            # Check if symbols in OHLCV tables have corresponding mappings
            logger.info("📊 Symbol Mapping Usage Analysis:")
            
            # Check equity symbols
            result = db.execute(text("""
                SELECT DISTINCT e.symbol, 
                       CASE WHEN sm.nse_symbol IS NOT NULL THEN 'Yes' ELSE 'No' END as has_mapping
                FROM equity_ohlcv e
                LEFT JOIN symbol_mapping sm ON e.symbol = sm.nse_symbol AND sm.market_type = 'EQUITY'
                ORDER BY e.symbol
                LIMIT 10
            """))
            
            equity_samples = result.fetchall()
            logger.info("  Sample equity symbols and their mappings:")
            for symbol, has_mapping in equity_samples:
                logger.info(f"    {symbol}: {has_mapping}")
            
            # Count symbols with and without mappings
            result = db.execute(text("""
                SELECT 
                    COUNT(DISTINCT e.symbol) as total_symbols,
                    COUNT(DISTINCT sm.nse_symbol) as mapped_symbols
                FROM equity_ohlcv e
                LEFT JOIN symbol_mapping sm ON e.symbol = sm.nse_symbol AND sm.market_type = 'EQUITY'
            """))
            
            total_symbols, mapped_symbols = result.fetchone()
            unmapped_symbols = total_symbols - mapped_symbols
            
            logger.info(f"  Equity symbols in OHLCV data: {total_symbols}")
            logger.info(f"  Symbols with mappings: {mapped_symbols}")
            logger.info(f"  Symbols without mappings: {unmapped_symbols}")
            
            if unmapped_symbols > 0:
                logger.warning(f"⚠️  {unmapped_symbols} equity symbols don't have symbol mappings")
            else:
                logger.info("✅ All equity symbols have mappings")
            
            return unmapped_symbols == 0
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in symbol mapping validation: {e}")
        return False


def validate_data_freshness():
    """Validate that data is recent and up-to-date."""
    logger.info("\n🔍 Validating data freshness...")
    
    try:
        db = next(get_db())
        
        try:
            # Check the most recent data in each table
            tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
            
            logger.info("📊 Data Freshness Analysis:")
            current_time = datetime.now()
            
            for table in tables:
                try:
                    result = db.execute(text(f"SELECT MAX(datetime) FROM {table}"))
                    max_date = result.scalar()
                    
                    if max_date:
                        # Calculate age of most recent data
                        if isinstance(max_date, str):
                            max_date = datetime.fromisoformat(max_date.replace('Z', '+00:00'))
                        
                        age_days = (current_time - max_date).days
                        logger.info(f"  {table}: Latest data from {max_date} ({age_days} days ago)")
                        
                        if age_days > 7:
                            logger.warning(f"    ⚠️  Data is {age_days} days old")
                        else:
                            logger.info(f"    ✅ Data is recent")
                    else:
                        logger.info(f"  {table}: No data found")
                        
                except Exception as e:
                    logger.error(f"    Error checking {table}: {e}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in data freshness validation: {e}")
        return False


def main():
    """Main validation function."""
    logger.info("🚀 Starting Data Integrity Validation")
    logger.info("=" * 80)
    
    validations = [
        ("Symbol Data Integrity", validate_symbol_data_integrity),
        ("OHLCV Data Counts", validate_ohlcv_data_counts),
        ("Symbol Mapping Usage", validate_symbol_mapping_usage),
        ("Data Freshness", validate_data_freshness),
    ]
    
    passed = 0
    failed = 0
    
    for validation_name, validation_func in validations:
        logger.info(f"\n{'='*20} {validation_name} {'='*20}")
        try:
            if validation_func():
                logger.info(f"✅ {validation_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {validation_name} FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {validation_name} FAILED with exception: {e}")
            failed += 1
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 VALIDATION SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Total validations: {passed + failed}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {(passed/(passed+failed)*100):.1f}%" if (passed+failed) > 0 else "N/A")
    
    return failed == 0


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
