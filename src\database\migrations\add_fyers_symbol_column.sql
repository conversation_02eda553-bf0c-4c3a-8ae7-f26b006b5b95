-- Migration script to add fyers_symbol column to OHLCV tables
-- Run this script to update existing database schema

-- Add fyers_symbol column to equity_ohlcv table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'equity_ohlcv' AND column_name = 'fyers_symbol'
    ) THEN
        ALTER TABLE equity_ohlcv ADD COLUMN fyers_symbol text;
        CREATE INDEX IF NOT EXISTS idx_equity_fyers_symbol ON equity_ohlcv (fyers_symbol);
        RAISE NOTICE 'Added fyers_symbol column to equity_ohlcv table';
    ELSE
        RAISE NOTICE 'fyers_symbol column already exists in equity_ohlcv table';
    END IF;
END $$;

-- Add fyers_symbol column to index_ohlcv table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'index_ohlcv' AND column_name = 'fyers_symbol'
    ) THEN
        ALTER TABLE index_ohlcv ADD COLUMN fyers_symbol text;
        CREATE INDEX IF NOT EXISTS idx_index_fyers_symbol ON index_ohlcv (fyers_symbol);
        RAISE NOTICE 'Added fyers_symbol column to index_ohlcv table';
    ELSE
        RAISE NOTICE 'fyers_symbol column already exists in index_ohlcv table';
    END IF;
END $$;

-- Add fyers_symbol column to futures_ohlcv table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'futures_ohlcv' AND column_name = 'fyers_symbol'
    ) THEN
        ALTER TABLE futures_ohlcv ADD COLUMN fyers_symbol text;
        CREATE INDEX IF NOT EXISTS idx_futures_fyers_symbol ON futures_ohlcv (fyers_symbol);
        RAISE NOTICE 'Added fyers_symbol column to futures_ohlcv table';
    ELSE
        RAISE NOTICE 'fyers_symbol column already exists in futures_ohlcv table';
    END IF;
END $$;

-- Add fyers_symbol column to options_ohlcv table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'options_ohlcv' AND column_name = 'fyers_symbol'
    ) THEN
        ALTER TABLE options_ohlcv ADD COLUMN fyers_symbol text;
        CREATE INDEX IF NOT EXISTS idx_options_fyers_symbol ON options_ohlcv (fyers_symbol);
        RAISE NOTICE 'Added fyers_symbol column to options_ohlcv table';
    ELSE
        RAISE NOTICE 'fyers_symbol column already exists in options_ohlcv table';
    END IF;
END $$;

-- Ensure TimescaleDB hypertables are created for all OHLCV tables
DO $$
BEGIN
    -- Check and create hypertable for equity_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'equity_ohlcv'
    ) THEN
        PERFORM create_hypertable('equity_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
        RAISE NOTICE 'Created hypertable for equity_ohlcv';
    ELSE
        RAISE NOTICE 'Hypertable already exists for equity_ohlcv';
    END IF;

    -- Check and create hypertable for index_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'index_ohlcv'
    ) THEN
        PERFORM create_hypertable('index_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
        RAISE NOTICE 'Created hypertable for index_ohlcv';
    ELSE
        RAISE NOTICE 'Hypertable already exists for index_ohlcv';
    END IF;

    -- Check and create hypertable for futures_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'futures_ohlcv'
    ) THEN
        PERFORM create_hypertable('futures_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
        RAISE NOTICE 'Created hypertable for futures_ohlcv';
    ELSE
        RAISE NOTICE 'Hypertable already exists for futures_ohlcv';
    END IF;

    -- Check and create hypertable for options_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'options_ohlcv'
    ) THEN
        PERFORM create_hypertable('options_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
        RAISE NOTICE 'Created hypertable for options_ohlcv';
    ELSE
        RAISE NOTICE 'Hypertable already exists for options_ohlcv';
    END IF;
    RAISE NOTICE 'Migration completed: Added fyers_symbol columns and ensured TimescaleDB hypertables';
END $$;
