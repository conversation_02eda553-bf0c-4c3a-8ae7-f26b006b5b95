#!/usr/bin/env python3
"""
Test script to verify symbol processing functionality
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.logging import get_logger

logger = get_logger(__name__)

def test_symbol_patterns():
    """Test symbol pattern matching"""
    processor = NSESymbolProcessor()
    
    test_symbols = [
        # EQUITY patterns
        'RELIANCE-EQ',
        'TCS-EQ',
        'INFY-EQ',
        'M&M-EQ',  # Test & character

        # INDEX patterns
        'NIFTY50-INDEX',
        'BANKNIFTY-INDEX',
        'FINNIFTY-INDEX',

        # FUTURES patterns
        'RELIANCE25JULFUT',
        'NIFTY25JULFUT',
        'BANKNIFTY25JULFUT',
        'M&M25JULFUT',  # Test & character
        'BAJAJ-AUTO25JULFUT',  # Test - character

        # OPTIONS monthly patterns
        'RELIANCE25JUL2500CE',
        'RELIANCE25JUL2500PE',
        'NIFTY25JUL25000CE',
        'M&M25JUL1700CE',  # Test & character
        'M&M25JUL1700PE',  # Test & character
        'BAJAJ-AUTO25JUL5900CE',  # Test - character
        'BAJAJ-AUTO25JUL5900PE',  # Test - character

        # OPTIONS weekly patterns
        'NIFTY2572425050CE',
        'BANKNIFTY2572450000PE',
        'FINNIFTY2572422000CE',

        # Invalid patterns (should be skipped)
        'INVALID-SYMBOL',
        'RANDOM123',
        'NSE:INVALID'
    ]
    
    logger.info("Testing symbol pattern matching:")
    for symbol in test_symbols:
        symbol_info = processor._extract_symbol_info(symbol)
        if symbol_info['market_type']:
            logger.info(f"✅ {symbol} -> {symbol_info['market_type']} ({symbol_info['underlying']})")
        else:
            logger.info(f"❌ {symbol} -> SKIPPED")

def test_csv_sample():
    """Test processing a small sample of CSV data"""
    import pandas as pd
    
    # Create sample data that mimics NSE CSV format (symbol_name has full Fyers format)
    sample_data = [
        # EQUITY samples
        ['1', 'Reliance Industries Ltd', 1, 1, 0.05, 'INE002A01018', '09:15-15:30', '2025-07-25', '', 'NSE:RELIANCE-EQ', 1, 1, 1, 'RELIANCE', 1, 1, 'EQ', '', '', '', ''],
        ['2', 'Tata Consultancy Services Ltd', 1, 1, 0.05, 'INE467B01029', '09:15-15:30', '2025-07-25', '', 'NSE:TCS-EQ', 1, 2, 2, 'TCS', 2, 1, 'EQ', '', '', '', ''],

        # INDEX samples
        ['3', 'Nifty 50', 2, 1, 0.05, '', '09:15-15:30', '2025-07-25', '', 'NSE:NIFTY50-INDEX', 2, 3, 3, 'NIFTY50', 3, 1, 'INDEX', '', '', '', ''],

        # FUTURES samples
        ['4', 'Reliance Industries Ltd', 3, 250, 0.05, '', '09:15-15:30', '2025-07-25', 1753142400, 'NSE:RELIANCE25JULFUT', 3, 4, 4, 'RELIANCE25JULFUT', 4, 250, 'FUT', '', 'RELIANCE', '', ''],

        # OPTIONS samples
        ['5', 'Reliance Industries Ltd', 4, 250, 0.05, '', '09:15-15:30', '2025-07-25', 1753142400, 'NSE:RELIANCE25JUL2500CE', 4, 5, 5, 'RELIANCE25JUL2500CE', 5, 250, 'OPT', '', 'RELIANCE', 'CE', 2500.0],
        ['6', 'Nifty 50', 4, 50, 0.05, '', '09:15-15:30', '2025-07-25', 1753142400, 'NSE:NIFTY2572425050CE', 4, 6, 6, 'NIFTY2572425050CE', 6, 50, 'OPT', '', 'NIFTY', 'CE', 25050.0],
    ]
    
    columns = [
        'fytoken', 'company_name', 'segment', 'lot_size', 'tick_size',
        'isin', 'trading_hours', 'last_update_date', 'expiry_timestamp',
        'symbol_name', 'exchange_segment', 'exchange_instrument_id',
        'instrument_id', 'symbol', 'token', 'minimum_lot_size',
        'instrument_type', 'underlying_fytoken', 'underlying_symbol',
        'option_type', 'strike_price'
    ]
    
    df = pd.DataFrame(sample_data, columns=columns)
    df['source_file'] = 'TEST'
    
    processor = NSESymbolProcessor()
    
    logger.info("\nTesting CSV sample processing:")
    filtered_df = processor._filter_relevant_symbols(df)
    
    if not filtered_df.empty:
        logger.info(f"✅ Filtered {len(filtered_df)} symbols from {len(df)} total")
        
        # Show symbol counts by type
        symbol_counts = filtered_df['market_type'].value_counts().to_dict()
        logger.info(f"Symbol counts: {symbol_counts}")
        
        # Show sample symbols
        for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
            sample = filtered_df[filtered_df['market_type'] == market_type]
            if not sample.empty:
                symbol = sample.iloc[0]['symbol_name']
                nse_symbol = sample.iloc[0]['nse_symbol']
                logger.info(f"  {market_type}: {symbol} -> {nse_symbol}")
    else:
        logger.error("❌ No symbols were filtered")

if __name__ == "__main__":
    logger.info("🧪 Starting symbol processing tests...")
    
    try:
        test_symbol_patterns()
        test_csv_sample()
        logger.info("\n✅ All tests completed successfully!")
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)
