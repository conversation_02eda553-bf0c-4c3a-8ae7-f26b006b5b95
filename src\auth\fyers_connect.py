"""
Module for Fyers API integration.
Handles authentication, data fetching, and market data operations.
"""
import os
import json
import logging
import time
import pytz
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

import pandas as pd
from fyers_apiv3 import fyersModel
from fyers_apiv3.FyersWebsocket.data_ws import FyersDataSocket
from .config_loader import get_config

# Configure logging
logger = logging.getLogger(__name__)

class FyersConnect:
    # Mapping of intervals to Fyers API resolutions
    INTERVAL_MAP = {
        "1D": "1D",   # Daily candle
        "1W": "7D",   # For weekly, we'll fetch daily data and aggregate
        "1": "1",     # 1 minute
        "3": "3",     # 3 minutes
        "5": "5",     # 5 minutes
        "15": "15",   # 15 minutes
        "30": "30",   # 30 minutes
        "60": "60"    # 1 hour
    }

    def __init__(self, fyers_config):
        """
        Initialize Fyers API connection.

        Args:
            fyers_config: An instance of FyersConfig.
        """
        self.fyers_auth = fyers_config
        self.client_id = self.fyers_auth.config['client_id']
        self.log_path = 'logs'
        self.access_token = None
        self.fyers = None
        self.ws = None

        # Performance optimization: Cache for spot prices to avoid redundant API calls
        self._spot_price_cache = {}
        self._cache_timestamp = {}
        self._cache_ttl = 300  # Cache TTL in seconds (5 minutes)

        # Performance optimization: Cache for option chain data
        self._option_chain_cache = {}
        self._option_chain_timestamp = {}
        self._option_chain_ttl = 600  # Cache TTL in seconds (10 minutes)

        # Performance optimization: Cache for OHLC data
        self._ohlc_cache = {}
        self._ohlc_timestamp = {}
        self._ohlc_ttl = 3600  # Cache TTL in seconds (1 hour)

        # Generic error handling: Track symbols that consistently fail option chain fetching
        self._failed_option_symbols = set()
        self._failure_count = {}
        self._max_failures_before_skip = 3  # Skip symbol after 3 consecutive failures

        # Performance optimization: Cache cleanup threshold
        self._max_cache_entries = 1000  # Maximum cache entries before cleanup

        # Dynamically load index symbols from NSE_CM.csv
        self.index_symbols = self._load_index_symbols()

        # Create dynamic mapping for index symbols
        self.index_symbol_mapping = self.get_dynamic_index_symbol_mapping()

    def _cleanup_expired_cache(self):
        """Clean up expired cache entries to prevent memory bloat."""
        current_time = time.time()

        # Clean up spot price cache
        expired_keys = [key for key, timestamp in self._cache_timestamp.items()
                       if current_time - timestamp > self._cache_ttl]
        for key in expired_keys:
            self._spot_price_cache.pop(key, None)
            self._cache_timestamp.pop(key, None)

        # Clean up option chain cache
        expired_keys = [key for key, timestamp in self._option_chain_timestamp.items()
                       if current_time - timestamp > self._option_chain_ttl]
        for key in expired_keys:
            self._option_chain_cache.pop(key, None)
            self._option_chain_timestamp.pop(key, None)

        # Clean up OHLC cache
        expired_keys = [key for key, timestamp in self._ohlc_timestamp.items()
                       if current_time - timestamp > self._ohlc_ttl]
        for key in expired_keys:
            self._ohlc_cache.pop(key, None)
            self._ohlc_timestamp.pop(key, None)

        logger.debug(f"Cache cleanup completed. Removed {len(expired_keys)} expired entries")

    def _load_index_symbols(self):
        """
        Load all index symbols from NSE_CM.csv (symbols ending with -INDEX)
        NSE_CM.csv format: Column 9 contains NSE symbol, Column 13 contains underlying symbol
        """
        import pandas as pd
        cm_csv_path = 'NSE_CM.csv'  # Adjust path if needed
        if not os.path.exists(cm_csv_path):
            logger.warning(f"NSE_CM.csv not found at {cm_csv_path}. Index symbol mapping may be incomplete.")
            return []
        try:
            # Read CSV without headers since NSE_CM.csv doesn't have column headers
            df = pd.read_csv(cm_csv_path, header=None)

            # Column 9 (0-indexed) contains the NSE symbol (e.g., NSE:NIFTY50-INDEX)
            # Filter for INDEX symbols
            if len(df.columns) > 9:
                index_mask = df[9].str.contains('-INDEX', na=False)
                index_symbols = df[index_mask][9].str.upper().tolist()
                logger.info(f"Loaded {len(index_symbols)} INDEX symbols from NSE_CM.csv")
                return index_symbols
            else:
                logger.warning("NSE_CM.csv has insufficient columns. Index symbol mapping may be incomplete.")
                return []
        except Exception as e:
            logger.error(f"Error loading index symbols from NSE_CM.csv: {e}")
            return []

    def get_dynamic_index_symbol_mapping(self) -> Dict[str, str]:
        """
        Create dynamic mapping from user-configured symbols to actual NSE INDEX symbols.

        Returns:
            Dictionary mapping user symbols to NSE INDEX symbols
        """
        mapping = {}
        try:
            import pandas as pd
            cm_csv_path = 'NSE_CM.csv'
            if not os.path.exists(cm_csv_path):
                return mapping

            # Read CSV without headers
            df = pd.read_csv(cm_csv_path, header=None)

            if len(df.columns) > 13:
                # Filter for INDEX symbols
                index_mask = df[9].str.contains('-INDEX', na=False)
                index_df = df[index_mask]

                # Create mapping from underlying (column 13) to NSE symbol (column 9)
                for _, row in index_df.iterrows():
                    nse_symbol = row[9]  # NSE:NIFTY50-INDEX
                    underlying = row[13]  # NIFTY, BANKNIFTY, etc.

                    if pd.notna(nse_symbol) and pd.notna(underlying):
                        # Map both the underlying and common variations
                        mapping[underlying.upper()] = nse_symbol.upper()

                        # Special mappings for common user inputs
                        if underlying.upper() == 'NIFTY':
                            mapping['NIFTY50'] = nse_symbol.upper()
                        elif underlying.upper() == 'BANKNIFTY':
                            mapping['NIFTYBANK'] = nse_symbol.upper()

                        # Also map the symbol name itself (e.g., NIFTY50-INDEX -> NIFTY50)
                        if '-INDEX' in nse_symbol.upper():
                            symbol_name = nse_symbol.upper().replace('NSE:', '').replace('-INDEX', '')
                            mapping[symbol_name] = nse_symbol.upper()

                logger.info(f"Created dynamic INDEX symbol mapping with {len(mapping)} entries")

        except Exception as e:
            logger.error(f"Error creating dynamic INDEX symbol mapping: {e}")

        return mapping

    def login(self) -> bool:
        """
        Authenticate with Fyers API using integrated authentication system.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication process...")
            
            access_token = self.fyers_auth.authenticate()
            if not access_token:
                logger.error("Authentication failed")
                return False
            
            self.access_token = access_token
            
            # Initialize Fyers model
            self.fyers = fyersModel.FyersModel(
                client_id=self.client_id,
                token=self.access_token,
                log_path=self.log_path
            )
            
            # Verify connection
            profile = self.fyers.get_profile()
            if profile and profile.get("code") == 200:
                logger.info("Successfully logged in to Fyers API")
                
                # Note: Spot price fetching moved to avoid duplicate logging
                # Spot prices will be fetched when needed by the scanning process
                
                return True
            else:
                logger.error(f"Failed to verify connection: {profile}")
                return False
                
        except Exception as e:
            logger.error(f"Error during Fyers login: {str(e)}")
            return False

    def get_ohlc_data(self, symbol: str, interval: str = "1D",
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch OHLC data for a symbol with rate limiting.

        Args:
            symbol: Trading symbol
            interval: Time interval (1D, 1W, etc.)
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            pd.DataFrame: OHLC data
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Get rate limiting configuration
            config = get_config()
            min_delay = config.rate_limit_min_delay_seconds
            max_retries = config.rate_limit_max_retries
            retry_backoff = config.rate_limit_retry_backoff

            # Format symbol for Fyers
            fyers_symbol = self._format_symbol(symbol)

            # Set default dates if not provided
            if not start_date:
                today = datetime.now()
                if interval == "1W":

                    return self.get_weekly_pivot_ohlc_data(symbol)
                else:
                    # For other intervals, use configured days_to_fetch
                    days_to_fetch = config.days_to_fetch
                    start_date = (today - timedelta(days=days_to_fetch)).strftime("%Y-%m-%d")
                    end_date = today.strftime("%Y-%m-%d")

            # For non-weekly intervals or when specific dates are provided
            data = {
                "symbol": fyers_symbol,
                "resolution": self.INTERVAL_MAP[interval],
                "date_format": "1",
                "range_from": start_date,
                "range_to": end_date or start_date,
                "cont_flag": "1"
            }

            # Implement retry logic with rate limiting
            for attempt in range(max_retries + 1):
                response = None  # Initialize response variable
                try:
                    response = self.fyers.history(data)

                    if response.get("code") == 200:
                        df = pd.DataFrame(response["candles"],
                                        columns=["timestamp", "open", "high", "low", "close", "volume"])
                        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
                        df.set_index("timestamp", inplace=True)

                        # Respect rate limit after successful request
                        time.sleep(min_delay)
                        return df
                    elif response.get("code") == 429:
                        # Rate limit hit - retry with exponential backoff
                        if attempt < max_retries:
                            wait_time = retry_backoff * (2 ** attempt)
                            logger.warning(f"Rate limit hit for {symbol}, attempt {attempt + 1}/{max_retries}. "
                                         f"Retrying after {wait_time:.1f} seconds...")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"Exceeded max retries for {symbol} due to rate limiting")
                            return pd.DataFrame()
                    else:
                        error_msg = f"Failed to fetch OHLC data: {response if response else 'No response received'}"
                        raise Exception(error_msg)

                except Exception as e:
                    if attempt < max_retries and "429" in str(e):
                        wait_time = retry_backoff * (2 ** attempt)
                        logger.warning(f"Rate limit error for {symbol}, attempt {attempt + 1}/{max_retries}. "
                                     f"Retrying after {wait_time:.1f} seconds...")
                        time.sleep(wait_time)
                    else:
                        raise e

        except Exception as e:
            logger.error(f"Error fetching OHLC data: {str(e)}")
            return pd.DataFrame()

    def get_weekly_pivot_ohlc_data(self, symbol: str) -> pd.DataFrame:
        """
        Fetch OHLC data for the previous week to calculate weekly pivot points.
        This method fetches daily data for the previous week and aggregates it
        into a single OHLC candle representing the previous week's trading.

        Args:
            symbol: Trading symbol (e.g., "NIFTY")

        Returns:
            pd.DataFrame: A DataFrame with a single row containing the previous week's
                          OHLC data, or an empty DataFrame if data cannot be fetched.
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            today = datetime.now()
            # Calculate the start and end of the previous week
            # Assuming week starts on Monday (0) and ends on Sunday (6)
            # We need data up to last Friday for weekly pivot calculation

            # Go back to the most recent Sunday
            days_since_sunday = today.weekday() + 1
            last_sunday = today - timedelta(days=days_since_sunday)

            # Previous week's Monday
            prev_week_monday = last_sunday - timedelta(days=6)
            # Previous week's Friday (last trading day)
            prev_week_friday = last_sunday - timedelta(days=2) # Sunday - 2 days = Friday

            start_date_str = prev_week_monday.strftime("%Y-%m-%d")
            end_date_str = prev_week_friday.strftime("%Y-%m-%d")


            daily_data_df = self._fetch_daily_ohlc_data_for_range(symbol, start_date_str, end_date_str)

            if daily_data_df.empty:
                logger.warning(f"No daily data found for {symbol} between {start_date_str} and {end_date_str}")
                return pd.DataFrame()

            # Aggregate daily data to form a single weekly candle
            weekly_open = daily_data_df["open"].iloc[0]
            weekly_high = daily_data_df["high"].max()
            weekly_low = daily_data_df["low"].min()
            weekly_close = daily_data_df["close"].iloc[-1]
            weekly_volume = daily_data_df["volume"].sum()

            # Validate weekly OHLC data
            if weekly_high == weekly_low == weekly_close:
                logger.warning(f"Weekly OHLC data has identical high, low, close values for {symbol}: "
                             f"H={weekly_high}, L={weekly_low}, C={weekly_close}. "
                             f"This indicates no trading activity or stale data.")
                return pd.DataFrame()  # Return empty DataFrame to skip this symbol

            # Additional validation for reasonable price variation
            if weekly_high > 0 and weekly_low > 0:
                price_variation_pct = ((weekly_high - weekly_low) / weekly_low) * 100
                if price_variation_pct < 0.01:  # Less than 0.01% variation
                    logger.warning(f"Very low price variation ({price_variation_pct:.4f}%) in weekly OHLC for {symbol}. "
                                 f"This may indicate insufficient trading activity.")

            logger.info(f"Weekly OHLC for {symbol}: O={weekly_open}, H={weekly_high}, L={weekly_low}, C={weekly_close}")

            weekly_ohlc = pd.DataFrame([{
                "timestamp": prev_week_friday, # Use Friday's date as the timestamp for the weekly candle
                "open": weekly_open,
                "high": weekly_high,
                "low": weekly_low,
                "close": weekly_close,
                "volume": weekly_volume
            }])
            weekly_ohlc["timestamp"] = pd.to_datetime(weekly_ohlc["timestamp"])
            weekly_ohlc.set_index("timestamp", inplace=True)
            
            return weekly_ohlc

        except Exception as e:
            logger.error(f"Error fetching weekly pivot OHLC data for {symbol}: {str(e)}")
            return pd.DataFrame()

    def _fetch_weekly_ohlc_for_option(self, symbol: str) -> Dict[str, float]:
        """
        Helper method to fetch OHLC data for the previous week for a specific option symbol.
        This method fetches daily data for the previous week and aggregates it
        into a single OHLC candle representing the previous week's trading.

        Args:
            symbol: Option trading symbol (e.g., "NSE:NIFTY2570325500PE")

        Returns:
            Dict: A dictionary with 'open', 'high', 'low', 'close' for the previous week,
                  or an empty dictionary if data cannot be fetched.
        """
        try:
            # Check cache first
            cache_key = f"weekly_ohlc_{symbol}"
            current_time = time.time()

            if (cache_key in self._ohlc_cache and
                cache_key in self._ohlc_timestamp and
                current_time - self._ohlc_timestamp[cache_key] < self._ohlc_ttl):
                logger.debug(f"Using cached weekly OHLC for {symbol}")
                return self._ohlc_cache[cache_key]

            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            today = datetime.now()
            # Calculate the start and end of the previous week
            # Assuming week starts on Monday (0) and ends on Sunday (6)
            # We need data up to last Friday for weekly pivot calculation

            # Go back to the most recent Sunday
            days_since_sunday = today.weekday() + 1
            last_sunday = today - timedelta(days=days_since_sunday)

            # Previous week's Monday
            prev_week_monday = last_sunday - timedelta(days=6)
            # Previous week's Friday (last trading day)
            prev_week_friday = last_sunday - timedelta(days=2) # Sunday - 2 days = Friday

            start_date_str = prev_week_monday.strftime("%Y-%m-%d")
            end_date_str = prev_week_friday.strftime("%Y-%m-%d")

            
            daily_data_df = self._fetch_daily_ohlc_data_for_range(symbol, start_date_str, end_date_str)

            if daily_data_df.empty:
                logger.warning(f"No daily data found for option {symbol} between {start_date_str} and {end_date_str}")
                return {}

            # Aggregate daily data to form a single weekly candle
            weekly_open = daily_data_df["open"].iloc[0]
            weekly_high = daily_data_df["high"].max()
            weekly_low = daily_data_df["low"].min()
            weekly_close = daily_data_df["close"].iloc[-1]

            # Validate weekly OHLC data for options
            if weekly_high == weekly_low == weekly_close:
                logger.warning(f"Weekly OHLC data has identical high, low, close values for option {symbol}: "
                             f"H={weekly_high}, L={weekly_low}, C={weekly_close}. "
                             f"This indicates no trading activity or stale data.")
                return {}  # Return empty dict to skip this symbol

            # Additional validation for reasonable price variation
            if weekly_high > 0 and weekly_low > 0:
                price_variation_pct = ((weekly_high - weekly_low) / weekly_low) * 100
                if price_variation_pct < 0.01:  # Less than 0.01% variation
                    logger.warning(f"Very low price variation ({price_variation_pct:.4f}%) in weekly OHLC for option {symbol}. "
                                 f"This may indicate insufficient trading activity.")

            result = {
                "open": weekly_open,
                "high": weekly_high,
                "low": weekly_low,
                "close": weekly_close
            }

            # Cache the result
            cache_key = f"weekly_ohlc_{symbol}"
            self._ohlc_cache[cache_key] = result
            self._ohlc_timestamp[cache_key] = time.time()

            return result

        except Exception as e:
            logger.error(f"Error fetching weekly OHLC data for option {symbol}: {str(e)}")
            return {}

    def _fetch_daily_ohlc_data_for_range(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Helper method to fetch daily OHLC data for a given date range with rate limiting.

        Args:
            symbol: Trading symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            pd.DataFrame: OHLC data for the specified range, or empty DataFrame.
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Get rate limiting configuration
            config = get_config()
            min_delay = config.rate_limit_min_delay_seconds
            max_retries = config.rate_limit_max_retries
            retry_backoff = config.rate_limit_retry_backoff

            fyers_symbol = self._format_symbol(symbol)
            data = {
                "symbol": fyers_symbol,
                "resolution": "1D",  # Always fetch daily for aggregation
                "date_format": "1",
                "range_from": start_date,
                "range_to": end_date,
                "cont_flag": "1"
            }

            # Implement retry logic with rate limiting
            for attempt in range(max_retries + 1):
                response = None  # Initialize response variable
                try:
                    response = self.fyers.history(data)

                    # Handle "no data" response gracefully
                    if response.get("s") == "no_data":
                        logger.warning(f"No historical data available for {fyers_symbol} in the given range.")
                        time.sleep(min_delay)  # Respect rate limit even for no data
                        return pd.DataFrame()

                    # Handle invalid symbol error
                    if response.get("code") == -300:
                        logger.warning(f"Invalid symbol provided: {fyers_symbol}. Original symbol: {symbol}")
                        time.sleep(min_delay)  # Respect rate limit even for invalid symbol
                        return pd.DataFrame()

                    if response.get("code") == 200:
                        df = pd.DataFrame(response["candles"],
                                        columns=["timestamp", "open", "high", "low", "close", "volume"])
                        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
                        df.set_index("timestamp", inplace=True)
                        df.sort_index(inplace=True) # Ensure data is sorted by timestamp

                        # Respect rate limit after successful request
                        time.sleep(min_delay)
                        return df
                    elif response.get("code") == 429:
                        # Rate limit hit - retry with exponential backoff
                        if attempt < max_retries:
                            wait_time = retry_backoff * (2 ** attempt)
                            logger.warning(f"Rate limit hit for {symbol}, attempt {attempt + 1}/{max_retries}. "
                                         f"Retrying after {wait_time:.1f} seconds...")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"Exceeded max retries for {symbol} due to rate limiting")
                            return pd.DataFrame()
                    else:
                        response_str = response if response else "No response received"
                        logger.warning(f"Failed to fetch daily OHLC data for {fyers_symbol}: {response_str}")
                        time.sleep(min_delay)  # Respect rate limit even for errors
                        return pd.DataFrame()

                except Exception as e:
                    if attempt < max_retries and "429" in str(e):
                        wait_time = retry_backoff * (2 ** attempt)
                        logger.warning(f"Rate limit error for {symbol}, attempt {attempt + 1}/{max_retries}. "
                                     f"Retrying after {wait_time:.1f} seconds...")
                        time.sleep(wait_time)
                    else:
                        raise e

        except Exception as e:
            logger.error(f"Error in _fetch_daily_ohlc_data_for_range: {str(e)}")
            return pd.DataFrame()

    def get_option_chain(self, symbol: str, expiry_type: str = "weekly", num_strikes_each_side: int = 10) -> List[Dict[str, Any]]:
        """
        Fetch option chain data for a symbol with generic failure tracking and caching.

        Args:
            symbol: Trading symbol
            expiry_type: Type of expiry to fetch ('weekly' or 'monthly')

        Returns:
            List[Dict]: Option chain data
        """
        try:
            # Check if this symbol has failed too many times
            if symbol in self._failed_option_symbols:
                logger.info(f"Skipping {symbol} - symbol has been marked as consistently failing for option chain fetching")
                return []

            # Check cache first
            cache_key = f"{symbol}_{expiry_type}_{num_strikes_each_side}"
            current_time = time.time()

            if (cache_key in self._option_chain_cache and
                cache_key in self._option_chain_timestamp and
                current_time - self._option_chain_timestamp[cache_key] < self._option_chain_ttl):
                logger.debug(f"Using cached option chain for {symbol}")
                return self._option_chain_cache[cache_key]

            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for spot price
            fyers_symbol = self._format_symbol(symbol)

            # Get expiry dates based on expiry_type
            today = datetime.now()
            expiry_dates = []

            if expiry_type.upper() == "WEEKLY":
                # Calculate next Thursday expiry for weekly
                days_to_thursday = (3 - today.weekday()) % 7  # 3 is Thursday
                if days_to_thursday == 0 and today.hour >= 15:  # After market hours on Thursday
                    days_to_thursday = 7  # Use next Thursday

                nearest_expiry = (today + timedelta(days=days_to_thursday)).date()
                expiry_dates.append(nearest_expiry)
                
            else:
                # For monthly expiry, find the last Thursday of current and/or next month
                from src.core.option_utils import get_last_thursday_of_month
                current_date = today.date()
                current_month = current_date.month
                current_year = current_date.year

                # Get the last Thursday of the current month
                last_thursday_current = get_last_thursday_of_month(current_year, current_month)

                # If the current month's expiry has already passed, use next month's expiry
                if current_date > last_thursday_current:
                    next_month = (current_month % 12) + 1
                    next_year = current_year + (1 if current_month == 12 else 0)
                    last_thursday_next = get_last_thursday_of_month(next_year, next_month)
                    expiry_dates.append(last_thursday_next)
                    
                else:
                    expiry_dates.append(last_thursday_current)
                    

            # Use the first expiry date for initial processing
            nearest_expiry = expiry_dates[0]
            

            spot_price_data = self._get_quote_data(symbol)
            if not spot_price_data:
                raise Exception(f"Failed to fetch spot price for {symbol} after multiple retries.")

            spot_price = float(spot_price_data.get("lp", 0.0))
            previous_close = float(spot_price_data.get("prev_close_price", 0.0))
                    

            # Get strike interval from config
            from core.symbol_config import get_symbol_config
            symbol_config_data = get_symbol_config(symbol, self)
            strike_interval = symbol_config_data.get("strike_interval", 50)
            num_strikes_each_side_from_config = symbol_config_data.get("num_strikes_each_side", 10)

            logger.debug(f"Spot Price for {symbol}: {spot_price}")
            logger.debug(f"Strike Interval for {symbol}: {strike_interval}")
            logger.debug(f"Num Strikes Each Side from config for {symbol}: {num_strikes_each_side_from_config}")

            # Calculate strikes centered around spot price
            if spot_price <= 0:
                logger.error(f"Invalid spot price for {symbol}: {spot_price}")
                return []
            base_strike = round(spot_price / strike_interval) * strike_interval
            potential_strikes = [
                base_strike + i * strike_interval
                for i in range(-num_strikes_each_side_from_config, num_strikes_each_side_from_config + 1)
            ]
            # Only keep positive strikes
            potential_strikes = [s for s in potential_strikes if s > 0]
            logger.debug(f"Generated Potential Strikes for {symbol}: {potential_strikes}")

            # Build option symbols list
            symbols_for_quote = []
            year = nearest_expiry.strftime('%y')
            month = str(nearest_expiry.month)  # Single digit month
            day = nearest_expiry.strftime('%d')

            # Import the option symbol formatter
            from src.core.option_utils import format_weekly_option_symbol, format_monthly_option_symbol
            
            for strike in potential_strikes:
                # Use the correct format for Fyers API: NSE:NIFTYYYMDD19000CE
                if expiry_type.upper() == "WEEKLY":
                    ce_symbol = format_weekly_option_symbol(self, symbol, nearest_expiry, int(strike), "CE")
                    pe_symbol = format_weekly_option_symbol(self, symbol, nearest_expiry, int(strike), "PE")
                else:
                    ce_symbol = format_monthly_option_symbol(self, symbol, nearest_expiry, int(strike), "CE")
                    pe_symbol = format_monthly_option_symbol(self, symbol, nearest_expiry, int(strike), "PE")
                
                # Add symbols to the list
                symbols_for_quote.extend([ce_symbol, pe_symbol])
            logger.debug(f"Symbols for Fyers API quote request for {symbol}: {symbols_for_quote}")
                
                

            # Process in batches
            processed_data = []
            for i in range(0, len(symbols_for_quote), 50):
                batch = symbols_for_quote[i:i + 50]
                logger.debug(f"Requesting quotes for batch: {batch}")
                options_response = self.fyers.quotes({"symbols": self._format_batch_symbols(batch)})
                logger.debug(f"Received response for batch: {options_response}")

                

                if not isinstance(options_response, dict) or options_response.get("code") != 200:
                    logger.error(f"Invalid response from Fyers API for batch: {options_response}")
                    logger.error(f"Batch contained {len(batch)} symbols")
                    # Log the first few symbols in the batch for debugging
                    if batch:
                        logger.error(f"First few symbols in batch: {', '.join(batch[:min(5, len(batch))])}")
                    continue

                quote_data = options_response.get("d", [])
                if not quote_data:
                    logger.error(f"No quote data returned from Fyers API for batch of {len(batch)} symbols")
                    continue

                for quote in quote_data:
                    if not isinstance(quote, dict):
                        continue

                    symbol_name = quote.get("n", "")
                    if not symbol_name:
                        continue

                    # Clean up the symbol name - remove any extra characters
                    symbol_name = symbol_name.strip()
                    # Remove any unexpected characters like quotes, brackets, etc.
                    symbol_name = re.sub(r"[\[\]']", "", symbol_name)
                    # Remove any trailing characters that aren't part of the symbol
                    symbol_name = re.sub(r"(NSE:[A-Z0-9]+(?:CE|PE)).*", r"\1", symbol_name)

                    

                    # Parse NSE:NIFTY2570325050CE format
                    try:
                        # Split into exchange and symbol parts
                        parts = symbol_name.split(":")
                        if len(parts) != 2:
                            logger.warning(f"Invalid symbol format (no exchange separator): {symbol_name}")
                            continue

                        option_part = parts[1].strip()  # NIFTY2570325050CE or NIFTY25JUL57000CE

                        # Choose regex based on expiry type
                        if expiry_type.upper() == "WEEKLY":
                            match = re.match(r"([A-Z]+)(\d{2})(0?[1-9]|1[0-2])(\d{2})(\d+)(CE|PE)", option_part)
                            if not match:
                                logger.warning(f"Could not parse weekly symbol '{symbol_name}': No match found")
                                continue
                            base_symbol, year, month, day, strike_str, opt_type = match.groups()
                        else:  # MONTHLY
                            match = re.match(r"([A-Z]+)(\d{2})([A-Z]{3})(\d+)(CE|PE)", option_part)
                            if not match:
                                logger.warning(f"Could not parse monthly symbol '{symbol_name}': No match found")
                                continue
                            base_symbol, year, month, strike_str, opt_type = match.groups()
                            day = None  # Day is not part of the monthly symbol format

                        strike = float(strike_str)
                        logger.debug(f"Parsed Strike: {strike}")

                        log_day = day if day is not None else "N/A"
                        logger.debug(f"Parsed Symbol: {base_symbol}, Year: {year}, Month: {month}, Day: {log_day}, Strike: {strike}, Type: {opt_type}")

                    except Exception as e:
                        logger.warning(f"Could not parse symbol '{symbol_name}': {e}")
                        continue

                    is_call = opt_type == 'CE'
                    option_values = quote.get("v", {})

                    logger.debug(f"Raw option_values for {symbol_name}: {option_values}")

                    # First try to get values from main response
                    ltp = float(option_values.get('lp', 0.0))
                    prev_close = float(option_values.get('previousClose', option_values.get('prev_close_price', 0.0)))

                    logger.debug(f"LTP: {ltp}, Prev Close: {prev_close}")

                    # Generic fallback mechanism for invalid option symbols
                    if (ltp == 0.0 or prev_close == 0.0) and (option_values.get('code', 0) == -300 or 'errmsg' in option_values):
                        logger.warning(f"Monthly options for {symbol} are not available or invalid (symbol: {symbol_name}). "
                                     f"This could be due to: 1) Symbol not having monthly options, 2) Incorrect symbol format, "
                                     f"3) Options not yet listed, or 4) Broker data issue. Skipping {symbol} from options processing.")

                        # Track failure for this symbol
                        self._failure_count[symbol] = self._failure_count.get(symbol, 0) + 1
                        if self._failure_count[symbol] >= self._max_failures_before_skip:
                            self._failed_option_symbols.add(symbol)
                            logger.info(f"Symbol {symbol} has failed {self._failure_count[symbol]} times. "
                                      f"Adding to skip list for future option chain requests.")

                        # Instead of raising an error, return empty list to skip this symbol gracefully
                        # This allows the system to continue processing other symbols
                        return []

                    # Calculate change and change percentage
                    change = 0.0
                    change_percent = 0.0
                    if prev_close > 0 and ltp > 0:
                        change = ltp - prev_close
                        change_percent = (change / prev_close) * 100.0
                    logger.debug(f"Calculated Change: {change}, Change Percent: {change_percent}")

                    # Use our calculated values, fallback to API values if available
                    ch = float(option_values.get('ch', change))
                    chp = float(option_values.get('chp', change_percent))
                
                    # Calculate time to expiry for reference
                    time_to_expiry = self._calculate_time_to_expiry(nearest_expiry)
                    logger.debug(f"Time to Expiry: {time_to_expiry}")
                    
                    # Import black_scholes_delta from option_utils
                    from src.core.option_utils import black_scholes_delta, implied_volatility
                    
                    # Default values for risk-free rate and implied volatility
                    risk_free_rate = 0.065  # 5%
                    
                    iv = implied_volatility(
                        symbol_name,
                        ltp, 
                        S=spot_price,
                        K=strike,
                        T=time_to_expiry,
                        r=risk_free_rate, 
                        option_type=opt_type
                    )
                    logger.debug(f"Implied Volatility (IV): {iv}")

                    delta = black_scholes_delta(
                        S=spot_price,
                        K=strike,
                        T=time_to_expiry,
                        r=risk_free_rate,
                        sigma=iv,
                        option_type=opt_type
                    )
                    logger.debug(f"Delta: {delta}")

                    # Find or create record for this strike
                    record = next((item for item in processed_data if item["strike"] == strike), None)
                    if record is None:
                        record = {
                            'strike': strike,
                            'expiry_date': nearest_expiry,
                            'call_symbol': "",
                            'put_symbol': "",
                            'call_price': 0.0,
                            'put_price': 0.0,
                            'call_volume': 0,
                            'put_volume': 0,
                            'call_oi': 0,
                            'put_oi': 0,
                            'call_iv': 0.0,
                            'put_iv': 0.0,
                            'call_change': 0.0,
                            'put_change': 0.0,
                            'call_delta': 0.0,  # Add delta field
                            'put_delta': 0.0,   # Add delta field
                            'spot_price': spot_price,
                            'time_to_expiry': time_to_expiry
                        }
                        processed_data.append(record)

                    # Get actual volume, OI, prev_close_price, and OHLC from API, don't simulate
                    # Use new keys if present, else fallback to old keys
                    volume = int(option_values.get('volume', option_values.get('v', 0)))
                    oi = int(option_values.get('oi', 0))
                    prev_close_price = float(option_values.get('prev_close_price', 0.0))
                    open_price = float(option_values.get('open_price', option_values.get('o', 0.0)))
                    high_price = float(option_values.get('high_price', option_values.get('h', 0.0)))
                    low_price = float(option_values.get('low_price', option_values.get('l', 0.0)))
                    close_price = float(option_values.get('close_price', option_values.get('c', 0.0)))

                    # Enhanced logging for debugging zero OHLCV/volume
                    if (volume == 0 and open_price == 0 and high_price == 0 and low_price == 0 and close_price == 0):
                        logger.error(f"API returned all zero OHLCV/volume for {symbol_name} (option_values={option_values})")
                        # Optionally, skip this record
                        # continue  # Do not continue here, fallback may provide valid OHLCV

                    # Separate logic for weekly and monthly expiry
                    if expiry_type.upper() == "WEEKLY":
                        if ltp == 0.0 and volume == 0:
                            logger.error(f"Weekly option symbol {symbol_name} is invalid or returned no data. Please set 'expiry_type' to 'MONTHLY' in your config.yaml and rerun.")
                            raise ValueError("Weekly option symbol is invalid. Please update config.yaml to use MONTHLY expiry.")
                    else:
                        if ltp == 0.0 and volume == 0:
                            logger.warning(f"Zero data for symbol {symbol_name}: option_values={option_values}")

                    # After fallback, skip if still all zero
                    if (volume == 0 and open_price == 0 and high_price == 0 and low_price == 0 and close_price == 0):
                        continue

                    # Update record with quote data
                    if is_call:
                        record['call_symbol'] = symbol_name
                        record['call_price'] = ltp
                        record['call_volume'] = volume
                        record['call_oi'] = oi
                        record['call_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['call_change'] = ch
                        record['call_change_percent'] = chp
                        record['call_delta'] = delta  # Add delta field
                        record['call_prev_close'] = prev_close_price
                        record['call_open'] = open_price
                        record['call_high'] = high_price
                        record['call_low'] = low_price
                        record['call_close'] = close_price
                    else:
                        record['put_symbol'] = symbol_name
                        record['put_price'] = ltp
                        record['put_volume'] = volume
                        record['put_oi'] = oi
                        record['put_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['put_change'] = ch
                        record['put_change_percent'] = chp
                        record['put_delta'] = delta  # Add delta field
                        record['put_prev_close'] = prev_close_price
                        record['put_open'] = open_price
                        record['put_high'] = high_price
                        record['put_low'] = low_price
                        record['put_close'] = close_price

                    

            # Sort by strike price
            processed_data.sort(key=lambda x: x['strike'])

            # Cache the result
            cache_key = f"{symbol}_{expiry_type}_{num_strikes_each_side}"
            self._option_chain_cache[cache_key] = processed_data
            self._option_chain_timestamp[cache_key] = time.time()

            return processed_data

        except Exception as e:
            if isinstance(e, ValueError):
                raise  # Re-raise the ValueError to be caught by the caller
            logger.error(f"Error fetching option chain: {str(e)}")
            return []

    def _is_weekly_expiry(self, expiry_date: str) -> bool:
        """
        Check if an expiry date is a weekly expiry.

        Args:
            expiry_date: Expiry date string from Fyers API

        Returns:
            bool: True if weekly expiry, False otherwise
        """
        try:
            from datetime import datetime
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
            # If it's a Thursday and not the last Thursday of the month
            return expiry.weekday() == 3 and expiry.day < 22
        except Exception:
            return False

    def _get_exchange_for_symbol(self, symbol_name: str) -> str:
        """
        Determines the exchange for a given symbol.
        """
        if symbol_name.upper() == "SENSEX":
            return "BSE"
        return "NSE"

    def _is_futures_symbol(self, symbol: str) -> bool:
        """
        Check if a symbol is a futures symbol.
        Futures symbols follow pattern: SYMBOL25JUL, SYMBOL25AUG, etc.
        """
        import re
        # Pattern for futures: SYMBOL followed by 2 digits (year) and 3 letters (month)
        futures_pattern = re.compile(r'^[A-Z0-9&]+\d{2}[A-Z]{3}(FUT)?$')
        return bool(futures_pattern.match(symbol.upper()))

    def _format_symbol(self, symbol: str) -> str:
        """
        Format symbol for Fyers API.

        Args:
            symbol: Trading symbol

        Returns:
            str: Formatted symbol
        """
        # Add symbol formatting logic based on Fyers requirements
        # Check if the symbol is already in Fyers format (e.g., NSE:NIFTY2570325200CE, NSE:DIXON25JULFUT)
        if symbol.startswith("NSE:") or symbol.startswith("BSE:"):
            return symbol

        exchange = self._get_exchange_for_symbol(symbol)
        symbol_upper = symbol.upper()

        # Handle index symbols using dynamic mapping
        if symbol_upper in self.index_symbol_mapping:
            return self.index_symbol_mapping[symbol_upper]
        elif symbol_upper.endswith('-INDEX'):
            # If already endswith -INDEX, use as is
            return f"{exchange}:{symbol_upper}"
        # Handle futures symbols (pattern: SYMBOL25JUL, SYMBOL25AUG, etc.)
        elif self._is_futures_symbol(symbol):
            if not symbol_upper.endswith("FUT"):
                symbol_upper = symbol_upper + "FUT"
            return f"{exchange}:{symbol_upper}"
        # Handle options symbols (CE/PE) - must be options format with strikes and expiry
        elif (symbol_upper.endswith("CE") or symbol_upper.endswith("PE")) and (
            # Check for monthly options pattern: SYMBOL25JUL1000CE
            re.match(r'^[A-Z0-9&]+\d{2}[A-Z]{3}\d+[CP]E$', symbol_upper) or
            # Check for weekly options pattern: SYMBOL2570725000CE
            re.match(r'^[A-Z0-9&]+\d{2}(0?[1-9]|1[0-2])\d{2}\d+[CP]E$', symbol_upper)
        ):
            return f"{exchange}:{symbol_upper}"
        # Default to equity format
        else:
            return f"{exchange}:{symbol_upper}-EQ"

    def _format_batch_symbols(self, batch):
        """
        Format a list of symbols into a comma-separated string for Fyers API.
        Args:
            batch (list): List of symbol strings
        Returns:
            str: Comma-separated symbols string
        """
        return ','.join(batch)

    def _calculate_time_to_expiry(self, expiry_date) -> float:
        """
        Calculate time to expiry in years.
        """
        if isinstance(expiry_date, str):
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
        else:
            expiry = datetime.combine(expiry_date, datetime.min.time())

        now = datetime.now()
        days_to_expiry = (expiry - now).days + 1  # Add 1 to include today
        return max(days_to_expiry / 365.0, 0.0001)  # Minimum value to avoid division by zero

    def _get_quote_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetches quote data for a symbol with retry logic.
        """
        fyers_symbol = self._format_symbol(symbol)
        try:
            for i in range(3): # Retry up to 3 times
                response = self.fyers.quotes({"symbols": fyers_symbol})
                if response.get("code") == 200 and response.get("d"):
                    spot_data = response["d"][0]
                    if "v" in spot_data and spot_data["v"].get("lp", 0.0) > 0:
                        return spot_data["v"]
                logger.warning(f"Attempt {i+1} to fetch quote for {symbol} failed. Response: {response}")
                time.sleep(1) # Wait before retrying
        except Exception as e:
            logger.error(f"Error fetching quote data for {symbol}: {e}")
        return None

    def get_spot_price(self, symbol: str) -> float:
        """
        Fetch the latest spot price for a given symbol using Fyers API with caching.
        Returns 0.0 if not available.
        """
        current_time = time.time()

        # Periodic cache cleanup to prevent memory bloat
        if len(self._spot_price_cache) > self._max_cache_entries:
            self._cleanup_expired_cache()

        # Check if we have a cached value that's still valid
        if (symbol in self._spot_price_cache and
            symbol in self._cache_timestamp and
            current_time - self._cache_timestamp[symbol] < self._cache_ttl):
            logger.debug(f"Using cached spot price for {symbol}: {self._spot_price_cache[symbol]}")
            return self._spot_price_cache[symbol]

        # Fetch fresh data
        quote_data = self._get_quote_data(symbol)
        if quote_data:
            spot_price = float(quote_data.get("lp", 0.0))
            # Cache the result
            self._spot_price_cache[symbol] = spot_price
            self._cache_timestamp[symbol] = current_time
            logger.debug(f"Fetched spot price for {symbol}: {spot_price}")  # Changed from INFO to DEBUG to reduce verbosity
            return spot_price
        return 0.0

    def clear_spot_price_cache(self):
        """Clear the spot price cache."""
        self._spot_price_cache.clear()
        self._cache_timestamp.clear()
        logger.debug("Spot price cache cleared")

    def get_cached_spot_prices(self) -> Dict[str, float]:
        """Get all cached spot prices for debugging."""
        return self._spot_price_cache.copy()

    def reset_failed_symbols(self):
        """Reset the failed symbols tracking - useful for new trading sessions."""
        self._failed_option_symbols.clear()
        self._failure_count.clear()
        logger.info("Reset failed symbols tracking")

    def get_failed_symbols(self) -> set:
        """Get the set of symbols that have been marked as consistently failing."""
        return self._failed_option_symbols.copy()
