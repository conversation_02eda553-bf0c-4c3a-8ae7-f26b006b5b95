"""
Robust Symbol Management Service.
Fetches and categorizes symbols from NSE_CM.csv and NSE_FO.csv with proper mapping.
"""

import logging
import requests
import pandas as pd
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timezone
import io

from src.database.models import Symbol, SymbolMapping, MarketType, OptionType
from src.core.config import settings

logger = logging.getLogger(__name__)


class SymbolService:
    """Service for managing symbols and symbol-related operations."""

    def __init__(self, db_session: Session):
        """Initialize the symbol service."""
        self.db = db_session
        self.fyers_api_urls = settings.general.fyers_api_url

    def fetch_nse_symbols(self, force_refresh: bool = False) -> Dict[str, int]:
        """
        Fetch symbols from NSE CSV files and update database.

        Args:
            force_refresh: Force refresh even if data exists

        Returns:
            Dictionary with counts of processed symbols
        """
        logger.info("Fetching NSE symbols from CSV files...")

        results = {
            "equity_count": 0,
            "index_count": 0,
            "futures_count": 0,
            "options_count": 0,
            "total_count": 0
        }

        try:
            # Process NSE_CM.csv (Cash Market - Equities and Indices)
            cm_url = self.fyers_api_urls[0]  # NSE_CM.csv
            cm_results = self._process_nse_cm_csv(cm_url, force_refresh)
            results["equity_count"] = cm_results["equity_count"]
            results["index_count"] = cm_results["index_count"]

            # Process NSE_FO.csv (Futures and Options)
            fo_url = self.fyers_api_urls[1]  # NSE_FO.csv
            fo_results = self._process_nse_fo_csv(fo_url, force_refresh)
            results["futures_count"] = fo_results["futures_count"]
            results["options_count"] = fo_results["options_count"]

            results["total_count"] = sum([
                results["equity_count"],
                results["index_count"],
                results["futures_count"],
                results["options_count"]
            ])

            logger.info(f"Symbol fetch completed: {results}")
            return results

        except Exception as e:
            logger.error(f"Error fetching NSE symbols: {e}")
            raise

    def _process_nse_cm_csv(self, url: str, force_refresh: bool) -> Dict[str, int]:
        """Process NSE_CM.csv file for equities and indices."""
        logger.info(f"Processing NSE_CM.csv from {url}")

        try:
            # Download CSV
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            # Read CSV
            df = pd.read_csv(io.StringIO(response.text))

            equity_count = 0
            index_count = 0

            for _, row in df.iterrows():
                try:
                    symbol_name = row.get('Fytoken', '')
                    trading_symbol = row.get('Symbol', '')
                    company_name = row.get('CompanyName', trading_symbol)
                    segment = row.get('Segment', '')

                    if not trading_symbol:
                        continue

                    # Determine market type based on segment or symbol characteristics
                    if 'INDEX' in segment.upper() or trading_symbol.endswith('INDEX'):
                        market_type = MarketType.INDEX
                        index_count += 1
                    else:
                        market_type = MarketType.EQUITY
                        equity_count += 1

                    # Create or update symbol
                    self._create_or_update_symbol(
                        symbol=trading_symbol,
                        name=company_name,
                        market_type=market_type,
                        fyers_symbol=symbol_name
                    )

                except Exception as e:
                    logger.warning(f"Error processing row: {e}")
                    continue

            self.db.commit()
            logger.info(f"NSE_CM processed: {equity_count} equities, {index_count} indices")

            return {"equity_count": equity_count, "index_count": index_count}

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error processing NSE_CM.csv: {e}")
            raise

    def _process_nse_fo_csv(self, url: str, force_refresh: bool) -> Dict[str, int]:
        """Process NSE_FO.csv file for futures and options."""
        logger.info(f"Processing NSE_FO.csv from {url}")

        try:
            # Download CSV
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            # Read CSV
            df = pd.read_csv(io.StringIO(response.text))

            futures_count = 0
            options_count = 0

            for _, row in df.iterrows():
                try:
                    symbol_name = row.get('Fytoken', '')
                    trading_symbol = row.get('Symbol', '')
                    company_name = row.get('CompanyName', trading_symbol)
                    segment = row.get('Segment', '')
                    expiry_date = row.get('Expiry', '')
                    strike_price = row.get('StrikePrice', 0)
                    option_type = row.get('OptionType', '')

                    if not trading_symbol:
                        continue

                    # Determine market type
                    if 'FUT' in segment.upper() or 'FUTURE' in segment.upper():
                        market_type = MarketType.FUTURES
                        futures_count += 1
                    elif 'OPT' in segment.upper() or 'OPTION' in segment.upper():
                        market_type = MarketType.OPTIONS
                        options_count += 1
                    else:
                        continue  # Skip unknown segments

                    # Parse expiry date
                    parsed_expiry = None
                    if expiry_date:
                        try:
                            parsed_expiry = pd.to_datetime(expiry_date).date()
                        except:
                            pass

                    # Parse option type
                    parsed_option_type = None
                    if option_type and market_type == MarketType.OPTIONS:
                        if option_type.upper() in ['CE', 'CALL']:
                            parsed_option_type = OptionType.CE
                        elif option_type.upper() in ['PE', 'PUT']:
                            parsed_option_type = OptionType.PE

                    # Create or update symbol
                    self._create_or_update_symbol(
                        symbol=trading_symbol,
                        name=company_name,
                        market_type=market_type,
                        fyers_symbol=symbol_name,
                        expiry_date=parsed_expiry,
                        strike_price=float(strike_price) if strike_price else None,
                        option_type=parsed_option_type
                    )

                except Exception as e:
                    logger.warning(f"Error processing F&O row: {e}")
                    continue

            self.db.commit()
            logger.info(f"NSE_FO processed: {futures_count} futures, {options_count} options")

            return {"futures_count": futures_count, "options_count": options_count}

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error processing NSE_FO.csv: {e}")
            raise

    def _create_or_update_symbol(self, symbol: str, name: str, market_type: MarketType,
                               fyers_symbol: str, expiry_date=None, strike_price=None,
                               option_type=None) -> None:
        """Create or update symbol and mapping records."""
        try:
            # Create or update symbol record
            existing_symbol = self.db.query(Symbol).filter(Symbol.symbol == symbol).first()

            if existing_symbol:
                existing_symbol.name = name
                existing_symbol.market_type = market_type
                existing_symbol.updated_at = datetime.now(timezone.utc)
            else:
                new_symbol = Symbol(
                    symbol=symbol,
                    name=name,
                    market_type=market_type,
                    exchange="NSE",
                    is_active=True
                )
                self.db.add(new_symbol)

            # Create or update symbol mapping
            existing_mapping = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == symbol,
                    SymbolMapping.market_type == market_type
                )
            ).first()

            if existing_mapping:
                existing_mapping.fyers_symbol = fyers_symbol
                existing_mapping.expiry_date = expiry_date
                existing_mapping.strike_price = strike_price
                existing_mapping.option_type = option_type
                existing_mapping.updated_at = datetime.now(timezone.utc)
            else:
                new_mapping = SymbolMapping(
                    nse_symbol=symbol,
                    fyers_symbol=fyers_symbol,
                    market_type=market_type,
                    expiry_date=expiry_date,
                    strike_price=strike_price,
                    option_type=option_type,
                    is_active=True
                )
                self.db.add(new_mapping)

        except Exception as e:
            logger.error(f"Error creating/updating symbol {symbol}: {e}")
            raise

    def get_symbols_by_market_type(self, market_type: MarketType, active_only: bool = True) -> List[Dict[str, str]]:
        """
        Get symbols by market type.

        Args:
            market_type: Market type enum
            active_only: Return only active symbols

        Returns:
            List of symbol dictionaries
        """
        try:
            query = self.db.query(Symbol).filter(Symbol.market_type == market_type)

            if active_only:
                query = query.filter(Symbol.is_active == True)

            symbols = query.all()

            return [
                {
                    "symbol": s.symbol,
                    "name": s.name,
                    "market_type": s.market_type.value,
                    "exchange": s.exchange
                }
                for s in symbols
            ]

        except Exception as e:
            logger.error(f"Error getting symbols by market type {market_type}: {e}")
            return []

    def get_symbol_mapping(self, nse_symbol: str, market_type: MarketType) -> Optional[Dict[str, str]]:
        """
        Get symbol mapping for NSE to Fyers conversion.

        Args:
            nse_symbol: NSE symbol
            market_type: Market type enum

        Returns:
            Symbol mapping dictionary or None
        """
        try:
            mapping = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == nse_symbol,
                    SymbolMapping.market_type == market_type,
                    SymbolMapping.is_active == True
                )
            ).first()

            if mapping:
                return {
                    "nse_symbol": mapping.nse_symbol,
                    "fyers_symbol": mapping.fyers_symbol,
                    "market_type": mapping.market_type.value,
                    "expiry_date": mapping.expiry_date.isoformat() if mapping.expiry_date else None,
                    "strike_price": float(mapping.strike_price) if mapping.strike_price else None,
                    "option_type": mapping.option_type.value if mapping.option_type else None
                }

            return None

        except Exception as e:
            logger.error(f"Error getting symbol mapping for {nse_symbol}: {e}")
            return None

    def get_all_active_symbols(self) -> List[str]:
        """Get all active symbols from database."""
        try:
            symbols = self.db.query(Symbol.symbol).filter(Symbol.is_active == True).all()
            return [symbol[0] for symbol in symbols]
        except Exception as e:
            logger.error(f"Error getting active symbols from database: {e}")
            return []

    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get symbol information from database."""
        try:
            symbol_obj = self.db.query(Symbol).filter(Symbol.symbol == symbol).first()
            if symbol_obj:
                return {
                    'id': symbol_obj.id,
                    'symbol': symbol_obj.symbol,
                    'name': symbol_obj.name,
                    'market_type': symbol_obj.market_type.value,
                    'exchange': symbol_obj.exchange,
                    'sector': symbol_obj.sector,
                    'industry': symbol_obj.industry,
                    'is_active': symbol_obj.is_active
                }
            return None
        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return None

    def search_symbols(self, search_term: str, market_type: MarketType = None, limit: int = 50) -> List[Dict]:
        """
        Search symbols by name or symbol.

        Args:
            search_term: Search term
            market_type: Optional market type filter
            limit: Maximum number of results

        Returns:
            List of matching symbols
        """
        try:
            query = self.db.query(Symbol).filter(
                and_(
                    Symbol.is_active == True,
                    or_(
                        Symbol.symbol.ilike(f"%{search_term}%"),
                        Symbol.name.ilike(f"%{search_term}%")
                    )
                )
            )

            if market_type:
                query = query.filter(Symbol.market_type == market_type)

            symbols = query.limit(limit).all()

            return [
                {
                    "symbol": s.symbol,
                    "name": s.name,
                    "market_type": s.market_type.value,
                    "exchange": s.exchange
                }
                for s in symbols
            ]

        except Exception as e:
            logger.error(f"Error searching symbols with term '{search_term}': {e}")
            return []