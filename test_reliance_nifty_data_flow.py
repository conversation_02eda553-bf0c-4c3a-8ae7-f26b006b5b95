#!/usr/bin/env python3
"""
Comprehensive test script for RELIANCE and NIFTY50 data flow.
Tests end-to-end data integrity for all market types.
"""

import sys
import logging
import asyncio
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from src.core.logging import setup_enhanced_logging, get_logger
from src.database.connection import get_db, check_database_connection, init_database
from src.services.data_management_service import DataManagementService
from src.services.bulk_data_service import BulkDataService
from src.core.nse_symbol_processor import NSESymbolProcessor
from src.database.models import MarketType

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


class RELIANCENIFTYTestSuite:
    """Comprehensive test suite for RELIANCE and NIFTY50 data flow."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.test_symbols = {
            MarketType.EQUITY: ['RELIANCE'],
            MarketType.INDEX: ['NIFTY50'],
            MarketType.FUTURES: ['RELIANCE'],
            MarketType.OPTIONS: ['RELIANCE']
        }
        self.test_results = {}
        
    async def run_comprehensive_test(self) -> bool:
        """Run comprehensive test suite."""
        logger.info("🚀 Starting RELIANCE & NIFTY50 Comprehensive Data Flow Test")
        logger.info("=" * 80)
        
        try:
            # Step 1: Database connection test
            if not await self._test_database_connection():
                return False
            
            # Step 2: Data management and cleanup
            if not await self._test_data_management():
                return False
            
            # Step 3: Symbol processing test
            if not await self._test_symbol_processing():
                return False
            
            # Step 4: Historical data fetching test
            if not await self._test_historical_data_fetching():
                return False
            
            # Step 5: Data integrity verification
            if not await self._test_data_integrity():
                return False
            
            # Step 6: Generate final report
            self._generate_final_report()
            
            logger.info("✅ All tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return False
    
    async def _test_database_connection(self) -> bool:
        """Test database connection and initialization."""
        logger.info("\n📊 Step 1: Testing Database Connection")
        
        try:
            # Check connection
            if not check_database_connection():
                logger.error("❌ Database connection failed")
                return False
            
            logger.info("✅ Database connection successful")
            
            # Initialize database if needed
            try:
                init_database()
                logger.info("✅ Database initialization completed")
            except Exception as e:
                logger.warning(f"⚠️ Database initialization warning: {e}")
            
            self.test_results['database_connection'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Database connection test failed: {e}")
            self.test_results['database_connection'] = False
            return False
    
    async def _test_data_management(self) -> bool:
        """Test data management and cleanup."""
        logger.info("\n🧹 Step 2: Testing Data Management")
        
        try:
            data_mgmt_service = DataManagementService()
            
            # Get initial health report
            logger.info("📋 Getting initial data health report...")
            initial_health = data_mgmt_service.get_data_health_report()
            
            initial_score = initial_health.get('overall_health', {}).get('score', 0)
            logger.info(f"Initial data health score: {initial_score:.1f}%")
            
            # Run comprehensive data management
            logger.info("🔧 Running comprehensive data management...")
            mgmt_results = data_mgmt_service.fix_all_data_issues(create_backup=True)
            
            # Get final health report
            final_health = data_mgmt_service.get_data_health_report()
            final_score = final_health.get('overall_health', {}).get('score', 0)
            
            logger.info(f"Final data health score: {final_score:.1f}%")
            
            # Check if improvement was made
            improvement = final_score >= initial_score
            if improvement:
                logger.info("✅ Data management completed successfully")
            else:
                logger.warning("⚠️ Data management may not have improved data quality")
            
            self.test_results['data_management'] = {
                'completed': True,
                'initial_score': initial_score,
                'final_score': final_score,
                'improvement': improvement,
                'results': mgmt_results
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Data management test failed: {e}")
            self.test_results['data_management'] = {'completed': False, 'error': str(e)}
            return False
    
    async def _test_symbol_processing(self) -> bool:
        """Test NSE symbol processing."""
        logger.info("\n📥 Step 3: Testing Symbol Processing")
        
        try:
            processor = NSESymbolProcessor()
            
            # Process NSE symbols
            logger.info("Processing NSE symbols...")
            results = processor.process_nse_files()
            
            # Check results
            all_successful = all(results.values())
            
            logger.info("📊 Symbol Processing Results:")
            for step, success in results.items():
                status = "✅" if success else "❌"
                logger.info(f"  {step}: {status}")
            
            if all_successful:
                logger.info("✅ Symbol processing completed successfully")
                
                # Get sample symbols for testing
                sample_symbols = processor.get_sample_symbols_by_type()
                if sample_symbols:
                    logger.info("🎯 Sample symbols found:")
                    for market_type, symbol in sample_symbols.items():
                        logger.info(f"  {market_type}: {symbol}")
                
            else:
                logger.warning("⚠️ Some symbol processing steps failed")
            
            self.test_results['symbol_processing'] = {
                'completed': all_successful,
                'results': results,
                'sample_symbols': sample_symbols if all_successful else {}
            }
            
            return all_successful
            
        except Exception as e:
            logger.error(f"❌ Symbol processing test failed: {e}")
            self.test_results['symbol_processing'] = {'completed': False, 'error': str(e)}
            return False
    
    async def _test_historical_data_fetching(self) -> bool:
        """Test historical data fetching for RELIANCE and NIFTY50."""
        logger.info("\n📈 Step 4: Testing Historical Data Fetching")
        
        try:
            bulk_service = BulkDataService()
            
            # Test data fetching for each market type
            fetch_results = {}
            
            for market_type, symbols in self.test_symbols.items():
                logger.info(f"Testing {market_type.value} data fetching for {symbols}...")
                
                try:
                    # Fetch 5 days of data for testing
                    results = await bulk_service.populate_all_market_types(
                        symbols_config={market_type: symbols},
                        days=5
                    )
                    
                    market_results = results.get(market_type, {})
                    successful_symbols = [s for s, success in market_results.items() if success]
                    
                    if successful_symbols:
                        logger.info(f"✅ {market_type.value}: Successfully fetched data for {successful_symbols}")
                        fetch_results[market_type.value] = {
                            'success': True,
                            'symbols': successful_symbols
                        }
                    else:
                        logger.warning(f"⚠️ {market_type.value}: No data fetched successfully")
                        fetch_results[market_type.value] = {
                            'success': False,
                            'symbols': []
                        }
                        
                except Exception as e:
                    logger.error(f"❌ {market_type.value}: Data fetching failed - {e}")
                    fetch_results[market_type.value] = {
                        'success': False,
                        'error': str(e)
                    }
            
            # Check overall success
            overall_success = any(result.get('success', False) for result in fetch_results.values())
            
            if overall_success:
                logger.info("✅ Historical data fetching test completed with some successes")
            else:
                logger.warning("⚠️ Historical data fetching had no successes")
            
            self.test_results['historical_data_fetching'] = {
                'completed': True,
                'overall_success': overall_success,
                'results': fetch_results
            }
            
            return overall_success
            
        except Exception as e:
            logger.error(f"❌ Historical data fetching test failed: {e}")
            self.test_results['historical_data_fetching'] = {'completed': False, 'error': str(e)}
            return False
    
    async def _test_data_integrity(self) -> bool:
        """Test data integrity after all operations."""
        logger.info("\n🔍 Step 5: Testing Data Integrity")
        
        try:
            data_mgmt_service = DataManagementService()
            
            # Get comprehensive health report
            health_report = data_mgmt_service.get_data_health_report()
            
            overall_health = health_report.get('overall_health', {})
            health_score = overall_health.get('score', 0)
            health_status = overall_health.get('status', 'UNKNOWN')
            
            logger.info(f"📊 Final Data Health Report:")
            logger.info(f"   Health Score: {health_score:.1f}%")
            logger.info(f"   Health Status: {health_status}")
            logger.info(f"   Issues Count: {overall_health.get('issues_count', 0)}")
            
            if overall_health.get('issues'):
                logger.warning("⚠️ Health Issues Found:")
                for issue in overall_health['issues']:
                    logger.warning(f"   - {issue}")
            
            # Consider test successful if health score is above 70%
            integrity_passed = health_score >= 70.0
            
            if integrity_passed:
                logger.info("✅ Data integrity test passed")
            else:
                logger.warning("⚠️ Data integrity test needs attention")
            
            self.test_results['data_integrity'] = {
                'completed': True,
                'passed': integrity_passed,
                'health_score': health_score,
                'health_status': health_status,
                'health_report': health_report
            }
            
            return integrity_passed
            
        except Exception as e:
            logger.error(f"❌ Data integrity test failed: {e}")
            self.test_results['data_integrity'] = {'completed': False, 'error': str(e)}
            return False
    
    def _generate_final_report(self) -> None:
        """Generate final test report."""
        logger.info("\n📋 Final Test Report")
        logger.info("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = 0
        
        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                if result.get('completed', False) or result.get('passed', False):
                    passed_tests += 1
                    status = "✅ PASSED"
                else:
                    status = "❌ FAILED"
            else:
                if result:
                    passed_tests += 1
                    status = "✅ PASSED"
                else:
                    status = "❌ FAILED"
            
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\nOverall Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 80:
            logger.info("🎉 Test suite PASSED with excellent results!")
        elif success_rate >= 60:
            logger.info("✅ Test suite PASSED with good results")
        else:
            logger.warning("⚠️ Test suite needs attention - some critical tests failed")


async def main():
    """Main test execution."""
    test_suite = RELIANCENIFTYTestSuite()
    success = await test_suite.run_comprehensive_test()
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)
