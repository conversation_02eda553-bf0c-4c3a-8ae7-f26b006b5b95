"""
Data Integrity Validator for NSE symbols and OHLCV data.
Validates data consistency across raw tables, symbol mapping, and OHLCV tables.
"""

import logging
import pandas as pd
import requests
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import psycopg2
from psycopg2.extras import RealDictCursor

from ..database.connection import engine
from ..core.config import settings

logger = logging.getLogger(__name__)


class DataIntegrityValidator:
    """Validates data integrity across NSE symbol processing pipeline."""
    
    def __init__(self):
        """Initialize the data integrity validator."""
        self.db_engine = engine
        self.validation_results = {
            'raw_data_integrity': {},
            'symbol_mapping_integrity': {},
            'ohlcv_table_integrity': {},
            'cross_table_consistency': {},
            'csv_database_parity': {},
            'missing_symbols_analysis': {},
            'duplicate_analysis': {},
            'summary': {}
        }
    
    def validate_all(self) -> Dict:
        """Run comprehensive data integrity validation."""
        logger.info("Starting comprehensive data integrity validation...")
        
        try:
            # 1. Validate raw data tables
            self._validate_raw_data_integrity()
            
            # 2. Validate symbol mapping table
            self._validate_symbol_mapping_integrity()
            
            # 3. Validate OHLCV table structures
            self._validate_ohlcv_table_integrity()
            
            # 4. Validate cross-table consistency
            self._validate_cross_table_consistency()

            # 5. Validate CSV-Database parity
            self._validate_csv_database_parity()

            # 6. Analyze missing symbols
            self._analyze_missing_symbols()

            # 7. Analyze duplicates
            self._analyze_duplicates()

            # 8. Generate summary
            self._generate_validation_summary()
            
            logger.info("Data integrity validation completed")
            return self.validation_results
            
        except Exception as e:
            logger.error(f"Error during data integrity validation: {e}")
            self.validation_results['error'] = str(e)
            return self.validation_results
    
    def _validate_raw_data_integrity(self) -> None:
        """Validate integrity of raw NSE data tables."""
        logger.info("Validating raw data integrity...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check NSE_CM raw data
                cursor.execute("SELECT COUNT(*) as total_rows FROM nse_cm_raw")
                cm_count = cursor.fetchone()['total_rows']
                
                cursor.execute("SELECT COUNT(DISTINCT symbol_name) as unique_symbols FROM nse_cm_raw")
                cm_unique = cursor.fetchone()['unique_symbols']
                
                # Check NSE_FO raw data
                cursor.execute("SELECT COUNT(*) as total_rows FROM nse_fo_raw")
                fo_count = cursor.fetchone()['total_rows']
                
                cursor.execute("SELECT COUNT(DISTINCT symbol_name) as unique_symbols FROM nse_fo_raw")
                fo_unique = cursor.fetchone()['unique_symbols']
                
                # Check for null/invalid data
                cursor.execute("""
                    SELECT COUNT(*) as null_symbols 
                    FROM nse_cm_raw 
                    WHERE symbol_name IS NULL OR symbol_name = ''
                """)
                cm_null_symbols = cursor.fetchone()['null_symbols']
                
                cursor.execute("""
                    SELECT COUNT(*) as null_symbols 
                    FROM nse_fo_raw 
                    WHERE symbol_name IS NULL OR symbol_name = ''
                """)
                fo_null_symbols = cursor.fetchone()['null_symbols']
                
                self.validation_results['raw_data_integrity'] = {
                    'nse_cm_raw': {
                        'total_rows': cm_count,
                        'unique_symbols': cm_unique,
                        'null_symbols': cm_null_symbols,
                        'data_quality': 'GOOD' if cm_null_symbols == 0 else 'ISSUES'
                    },
                    'nse_fo_raw': {
                        'total_rows': fo_count,
                        'unique_symbols': fo_unique,
                        'null_symbols': fo_null_symbols,
                        'data_quality': 'GOOD' if fo_null_symbols == 0 else 'ISSUES'
                    }
                }
                
            conn.close()
            logger.info("Raw data integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating raw data integrity: {e}")
            self.validation_results['raw_data_integrity']['error'] = str(e)
    
    def _validate_symbol_mapping_integrity(self) -> None:
        """Validate integrity of symbol mapping table."""
        logger.info("Validating symbol mapping integrity...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Total symbols in mapping
                cursor.execute("SELECT COUNT(*) as total_symbols FROM symbol_mapping")
                total_symbols = cursor.fetchone()['total_symbols']
                
                # Symbols by market type
                cursor.execute("""
                    SELECT market_type, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY market_type
                """)
                market_type_counts = {row['market_type']: row['count'] for row in cursor.fetchall()}
                
                # Active vs inactive symbols
                cursor.execute("""
                    SELECT is_active, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY is_active
                """)
                active_counts = {row['is_active']: row['count'] for row in cursor.fetchall()}
                
                # Check for duplicate NSE symbols
                cursor.execute("""
                    SELECT nse_symbol, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY nse_symbol 
                    HAVING COUNT(*) > 1
                """)
                duplicate_nse = cursor.fetchall()
                
                # Check for duplicate Fyers symbols
                cursor.execute("""
                    SELECT fyers_symbol, COUNT(*) as count 
                    FROM symbol_mapping 
                    WHERE fyers_symbol IS NOT NULL
                    GROUP BY fyers_symbol 
                    HAVING COUNT(*) > 1
                """)
                duplicate_fyers = cursor.fetchall()
                
                self.validation_results['symbol_mapping_integrity'] = {
                    'total_symbols': total_symbols,
                    'market_type_distribution': market_type_counts,
                    'active_distribution': active_counts,
                    'duplicate_nse_symbols': len(duplicate_nse),
                    'duplicate_fyers_symbols': len(duplicate_fyers),
                    'data_quality': 'GOOD' if len(duplicate_nse) == 0 and len(duplicate_fyers) == 0 else 'ISSUES'
                }
                
            conn.close()
            logger.info("Symbol mapping integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating symbol mapping integrity: {e}")
            self.validation_results['symbol_mapping_integrity']['error'] = str(e)
    
    def _validate_ohlcv_table_integrity(self) -> None:
        """Validate integrity of OHLCV tables structure and data."""
        logger.info("Validating OHLCV table integrity...")
        
        ohlcv_tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for table in ohlcv_tables:
                    # Check if table exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = %s
                        )
                    """, (table,))
                    table_exists = cursor.fetchone()['exists']
                    
                    if not table_exists:
                        self.validation_results['ohlcv_table_integrity'][table] = {
                            'exists': False,
                            'error': 'Table does not exist'
                        }
                        continue
                    
                    # Check if fyers_symbol column exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.columns 
                            WHERE table_name = %s AND column_name = 'fyers_symbol'
                        )
                    """, (table,))
                    fyers_column_exists = cursor.fetchone()['exists']
                    
                    # Check if it's a TimescaleDB hypertable
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM timescaledb_information.hypertables 
                            WHERE hypertable_name = %s
                        )
                    """, (table,))
                    is_hypertable = cursor.fetchone()['exists']
                    
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) as row_count FROM {table}")
                    row_count = cursor.fetchone()['row_count']
                    
                    self.validation_results['ohlcv_table_integrity'][table] = {
                        'exists': True,
                        'fyers_column_exists': fyers_column_exists,
                        'is_hypertable': is_hypertable,
                        'row_count': row_count,
                        'status': 'GOOD' if fyers_column_exists and is_hypertable else 'NEEDS_UPDATE'
                    }
                
            conn.close()
            logger.info("OHLCV table integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating OHLCV table integrity: {e}")
            self.validation_results['ohlcv_table_integrity']['error'] = str(e)
    
    def _validate_cross_table_consistency(self) -> None:
        """Validate consistency across different tables."""
        logger.info("Validating cross-table consistency...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check symbols in mapping vs raw tables
                cursor.execute("""
                    SELECT COUNT(DISTINCT nse_symbol) as mapped_symbols 
                    FROM symbol_mapping
                """)
                mapped_symbols = cursor.fetchone()['mapped_symbols']
                
                cursor.execute("""
                    SELECT COUNT(DISTINCT SPLIT_PART(symbol_name, ':', -1)) as raw_cm_symbols 
                    FROM nse_cm_raw
                """)
                raw_cm_symbols = cursor.fetchone()['raw_cm_symbols']
                
                cursor.execute("""
                    SELECT COUNT(DISTINCT SPLIT_PART(symbol_name, ':', -1)) as raw_fo_symbols 
                    FROM nse_fo_raw
                """)
                raw_fo_symbols = cursor.fetchone()['raw_fo_symbols']
                
                total_raw_symbols = raw_cm_symbols + raw_fo_symbols
                
                self.validation_results['cross_table_consistency'] = {
                    'mapped_symbols': mapped_symbols,
                    'raw_cm_symbols': raw_cm_symbols,
                    'raw_fo_symbols': raw_fo_symbols,
                    'total_raw_symbols': total_raw_symbols,
                    'mapping_coverage': round((mapped_symbols / total_raw_symbols * 100), 2) if total_raw_symbols > 0 else 0,
                    'consistency_status': 'GOOD' if mapped_symbols >= total_raw_symbols * 0.95 else 'ISSUES'
                }
                
            conn.close()
            logger.info("Cross-table consistency validation completed")
            
        except Exception as e:
            logger.error(f"Error validating cross-table consistency: {e}")
            self.validation_results['cross_table_consistency']['error'] = str(e)
    
    def _generate_validation_summary(self) -> None:
        """Generate overall validation summary."""
        logger.info("Generating validation summary...")
        
        issues = []
        recommendations = []
        
        # Check raw data issues
        raw_data = self.validation_results.get('raw_data_integrity', {})
        if raw_data.get('nse_cm_raw', {}).get('data_quality') == 'ISSUES':
            issues.append("NSE_CM raw data has null/invalid symbols")
            recommendations.append("Re-run NSE symbol processing with error handling")
        
        if raw_data.get('nse_fo_raw', {}).get('data_quality') == 'ISSUES':
            issues.append("NSE_FO raw data has null/invalid symbols")
            recommendations.append("Re-run NSE symbol processing with error handling")
        
        # Check symbol mapping issues
        mapping_data = self.validation_results.get('symbol_mapping_integrity', {})
        if mapping_data.get('data_quality') == 'ISSUES':
            issues.append("Symbol mapping has duplicate entries")
            recommendations.append("Clean up duplicate symbol mappings")
        
        # Check OHLCV table issues
        ohlcv_data = self.validation_results.get('ohlcv_table_integrity', {})
        for table, info in ohlcv_data.items():
            if isinstance(info, dict) and info.get('status') == 'NEEDS_UPDATE':
                if not info.get('fyers_column_exists'):
                    issues.append(f"{table} missing fyers_symbol column")
                    recommendations.append(f"Run migration to add fyers_symbol column to {table}")
                if not info.get('is_hypertable'):
                    issues.append(f"{table} is not a TimescaleDB hypertable")
                    recommendations.append(f"Convert {table} to TimescaleDB hypertable")
        
        # Check consistency issues
        consistency_data = self.validation_results.get('cross_table_consistency', {})
        if consistency_data.get('consistency_status') == 'ISSUES':
            issues.append("Low symbol mapping coverage")
            recommendations.append("Re-run symbol processing to improve mapping coverage")
        
        self.validation_results['summary'] = {
            'total_issues': len(issues),
            'issues': issues,
            'recommendations': recommendations,
            'overall_status': 'HEALTHY' if len(issues) == 0 else 'NEEDS_ATTENTION',
            'validation_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Validation summary: {len(issues)} issues found")

    def _validate_csv_database_parity(self) -> None:
        """Validate parity between CSV files and database tables."""
        logger.info("Validating CSV-Database parity...")

        try:
            # Download and analyze CSV files
            csv_data = self._download_and_analyze_csv_files()

            # Compare with database data
            db_data = self._get_database_symbol_counts()

            # Calculate parity
            parity_results = {}
            for source in ['NSE_CM', 'NSE_FO']:
                csv_count = csv_data.get(source, {}).get('total_symbols', 0)
                db_count = db_data.get(source.lower() + '_raw', {}).get('total_rows', 0)

                parity_percentage = (db_count / csv_count * 100) if csv_count > 0 else 0

                parity_results[source] = {
                    'csv_symbols': csv_count,
                    'db_records': db_count,
                    'parity_percentage': round(parity_percentage, 2),
                    'missing_count': max(0, csv_count - db_count),
                    'status': 'GOOD' if parity_percentage >= 99.0 else 'NEEDS_ATTENTION'
                }

            self.validation_results['csv_database_parity'] = parity_results
            logger.info("CSV-Database parity validation completed")

        except Exception as e:
            logger.error(f"Error validating CSV-Database parity: {e}")
            self.validation_results['csv_database_parity']['error'] = str(e)

    def _download_and_analyze_csv_files(self) -> Dict:
        """Download and analyze NSE CSV files."""
        csv_urls = {
            'NSE_CM': 'https://public.fyers.in/sym_details/NSE_CM.csv',
            'NSE_FO': 'https://public.fyers.in/sym_details/NSE_FO.csv'
        }

        csv_data = {}

        for source, url in csv_urls.items():
            try:
                logger.debug(f"Downloading {source} CSV from {url}")
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                # Parse CSV content
                from io import StringIO
                df = pd.read_csv(StringIO(response.text))

                csv_data[source] = {
                    'total_symbols': len(df),
                    'unique_symbols': df['Fytoken'].nunique() if 'Fytoken' in df.columns else len(df),
                    'columns': list(df.columns)
                }

                logger.debug(f"{source}: {csv_data[source]['total_symbols']} symbols found")

            except Exception as e:
                logger.error(f"Error downloading/analyzing {source} CSV: {e}")
                csv_data[source] = {'error': str(e)}

        return csv_data

    def _get_database_symbol_counts(self) -> Dict:
        """Get symbol counts from database tables."""
        db_data = {}

        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Count records in raw tables
                for table in ['nse_cm_raw', 'nse_fo_raw']:
                    cursor.execute(f"SELECT COUNT(*) as total_rows FROM {table}")
                    total_rows = cursor.fetchone()['total_rows']

                    cursor.execute(f"SELECT COUNT(DISTINCT fytoken) as unique_tokens FROM {table}")
                    unique_tokens = cursor.fetchone()['unique_tokens']

                    db_data[table] = {
                        'total_rows': total_rows,
                        'unique_tokens': unique_tokens
                    }

            conn.close()

        except Exception as e:
            logger.error(f"Error getting database symbol counts: {e}")
            db_data['error'] = str(e)

        return db_data

    def _analyze_missing_symbols(self) -> None:
        """Analyze missing symbols between expected and actual data."""
        logger.info("Analyzing missing symbols...")

        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            missing_analysis = {}

            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Analyze symbol mapping completeness
                cursor.execute("""
                    SELECT market_type, COUNT(*) as total_symbols,
                           COUNT(CASE WHEN fyers_symbol IS NOT NULL THEN 1 END) as with_fyers_symbol,
                           COUNT(CASE WHEN fyers_symbol IS NULL THEN 1 END) as missing_fyers_symbol
                    FROM symbol_mapping
                    GROUP BY market_type
                """)

                mapping_analysis = cursor.fetchall()

                for row in mapping_analysis:
                    market_type = row['market_type']
                    missing_analysis[market_type] = {
                        'total_symbols': row['total_symbols'],
                        'with_fyers_symbol': row['with_fyers_symbol'],
                        'missing_fyers_symbol': row['missing_fyers_symbol'],
                        'completion_percentage': round((row['with_fyers_symbol'] / row['total_symbols'] * 100), 2) if row['total_symbols'] > 0 else 0
                    }

                # Find symbols that should exist but don't
                cursor.execute("""
                    SELECT 'EQUITY' as expected_type, COUNT(*) as expected_count
                    FROM nse_cm_raw
                    WHERE instrument_type = 'EQ'
                    UNION ALL
                    SELECT 'INDEX' as expected_type, COUNT(*) as expected_count
                    FROM nse_cm_raw
                    WHERE instrument_type = 'INDEX'
                    UNION ALL
                    SELECT 'FUTURES' as expected_type, COUNT(*) as expected_count
                    FROM nse_fo_raw
                    WHERE instrument_type = 'FUT'
                    UNION ALL
                    SELECT 'OPTIONS' as expected_type, COUNT(*) as expected_count
                    FROM nse_fo_raw
                    WHERE instrument_type IN ('CE', 'PE')
                """)

                expected_counts = cursor.fetchall()

                for row in expected_counts:
                    market_type = row['expected_type']
                    expected_count = row['expected_count']

                    if market_type in missing_analysis:
                        actual_count = missing_analysis[market_type]['total_symbols']
                        missing_count = max(0, expected_count - actual_count)

                        missing_analysis[market_type].update({
                            'expected_from_raw': expected_count,
                            'actual_in_mapping': actual_count,
                            'missing_from_mapping': missing_count,
                            'mapping_parity_percentage': round((actual_count / expected_count * 100), 2) if expected_count > 0 else 0
                        })

            conn.close()

            self.validation_results['missing_symbols_analysis'] = missing_analysis
            logger.info("Missing symbols analysis completed")

        except Exception as e:
            logger.error(f"Error analyzing missing symbols: {e}")
            self.validation_results['missing_symbols_analysis']['error'] = str(e)

    def _analyze_duplicates(self) -> None:
        """Analyze duplicate entries in raw tables."""
        logger.info("Analyzing duplicate entries...")

        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            duplicate_analysis = {}

            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for table in ['nse_cm_raw', 'nse_fo_raw']:
                    # Get total rows
                    cursor.execute(f"SELECT COUNT(*) as total_rows FROM {table}")
                    total_rows = cursor.fetchone()['total_rows']

                    # Get unique rows based on key fields
                    if table == 'nse_cm_raw':
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id']
                    else:  # nse_fo_raw
                        key_fields = ['fytoken', 'symbol_name', 'exchange_segment', 'instrument_id', 'expiry_timestamp']

                    key_fields_str = ', '.join(key_fields)
                    cursor.execute(f"""
                        SELECT COUNT(DISTINCT ({key_fields_str})) as unique_rows
                        FROM {table}
                        WHERE {' AND '.join([f'{field} IS NOT NULL' for field in key_fields])}
                    """)
                    unique_rows = cursor.fetchone()['unique_rows']

                    # Find duplicate groups
                    cursor.execute(f"""
                        SELECT COUNT(*) as duplicate_groups
                        FROM (
                            SELECT {key_fields_str}
                            FROM {table}
                            WHERE {' AND '.join([f'{field} IS NOT NULL' for field in key_fields])}
                            GROUP BY {key_fields_str}
                            HAVING COUNT(*) > 1
                        ) duplicates
                    """)
                    duplicate_groups = cursor.fetchone()['duplicate_groups']

                    duplicate_rows = total_rows - unique_rows

                    duplicate_analysis[table] = {
                        'total_rows': total_rows,
                        'unique_rows': unique_rows,
                        'duplicate_rows': duplicate_rows,
                        'duplicate_groups': duplicate_groups,
                        'duplicate_percentage': round((duplicate_rows / total_rows * 100), 2) if total_rows > 0 else 0,
                        'status': 'CLEAN' if duplicate_rows == 0 else 'NEEDS_CLEANUP'
                    }

            conn.close()

            self.validation_results['duplicate_analysis'] = duplicate_analysis
            logger.info("Duplicate analysis completed")

        except Exception as e:
            logger.error(f"Error analyzing duplicates: {e}")
            self.validation_results['duplicate_analysis']['error'] = str(e)
